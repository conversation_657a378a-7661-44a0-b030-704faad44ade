# IM外部交互事件总线接口文档

## 概述

本文档详细说明了基于事件总线的IM与外部交互方案，包括事件类型定义、使用方法和最佳实践。该方案将原有的 `window.parent.postMessage` 通信方式重构为基于事件总线的通信机制，提供更好的类型安全和维护性。

## 架构设计

### 事件总线架构
- **基础库**: 基于 `mitt` 库实现的轻量级事件总线
- **类型安全**: 完整的 TypeScript 类型定义
- **命名规范**: 使用 `EXTERNAL_` 前缀区分外部交互事件
- **生命周期管理**: 自动的事件订阅和取消订阅机制

### 核心文件
- `src/utils/events.ts` - 事件总线核心实现和类型定义
- `src/pages/smartAssistantHalfPage/RenderParcel.tsx` - 事件接收和处理逻辑

## 事件类型定义

### 1. EXTERNAL_INPUT_CONTAINER_RESIZE
**用途**: 通知外部输入容器尺寸变化

**数据结构**:
```typescript
{
  height: number; // 输入容器的高度（像素）
}
```

**触发时机**: 
- 用户输入内容导致输入框高度变化时
- 输入框组件尺寸发生变化时

**使用场景**: 外部页面需要根据输入框高度调整布局

### 2. EXTERNAL_SMART_INPUT_CHANGED
**用途**: 通知外部智能输入内容变化

**数据结构**:
```typescript
{
  content: string; // 输入的内容
}
```

**触发时机**:
- 用户开启/关闭OA搜索功能时
- 输入内容清空时
- 智能助手输入内容发生变化时

**使用场景**: 外部页面需要根据输入内容提供搜索建议或其他交互

### 3. EXTERNAL_SMART_PAGE_CLICKED
**用途**: 通知外部智能助手页面被点击

**数据结构**:
```typescript
void // 无数据
```

**触发时机**: 用户点击智能助手页面任意区域时

**使用场景**: 外部页面需要知道用户与智能助手的交互状态

### 4. EXTERNAL_SMART_PAGE_LOADED
**用途**: 通知外部智能助手页面加载完成

**数据结构**:
```typescript
void // 无数据
```

**触发时机**: MessageList组件中的虚拟列表渲染完成时

**使用场景**: 外部页面需要知道智能助手何时准备就绪

### 5. EXTERNAL_BACK_BTN_CLICKED
**用途**: 通知外部返回按钮被点击

**数据结构**:
```typescript
void // 无数据
```

**触发时机**: Header组件中的返回按钮被点击时

**使用场景**: 外部页面需要处理返回操作，如关闭智能助手窗口或返回上一页

## 使用方法

### 外部页面集成

#### 1. 引入RenderParcel组件
```typescript
import RenderParcel, { RenderParcelRef } from '@/pages/smartAssistantHalfPage/RenderParcel';
import { useRef } from 'react';

// 创建ref用于调用消息处理函数
const renderParcelRef = useRef<RenderParcelRef>(null);

// 定义事件处理函数
const handleInputContainerResize = (data: { height: number }) => {
  console.log('输入容器高度变化:', data.height);
  // 根据高度调整外部页面布局
};

const handleSmartInputChanged = (content: string) => {
  console.log('智能输入内容变化:', content);
  // 处理输入内容变化，如提供搜索建议
};

const handleSmartPageClicked = () => {
  console.log('智能助手页面被点击');
  // 处理页面点击事件
};

const handleSmartPageLoaded = () => {
  console.log('智能助手页面加载完成');
  // 页面加载完成后的初始化操作
};

const handleBackBtnClicked = () => {
  console.log('返回按钮被点击');
  // 处理返回操作，如关闭智能助手窗口
};

// setHost回调函数，接收消息处理接口
const setHost = (host: RenderParcelRef) => {
  // 保存host接口，供后续使用
  console.log('接收到消息处理接口:', host);
};

// 使用组件
<RenderParcel
  ref={renderParcelRef}
  setHost={setHost}
  handleInputContainerResize={handleInputContainerResize}
  handleSmartInputChanged={handleSmartInputChanged}
  handleSmartPageClicked={handleSmartPageClicked}
  handleSmartPageLoaded={handleSmartPageLoaded}
  handleBackBtnClicked={handleBackBtnClicked}
/>
```

#### 2. 消息发送功能

**通过ref调用消息处理函数**:
```typescript
// 发送输入内容（不立即发送消息）
const sendInput = () => {
  renderParcelRef.current?.sendParentInput('这是输入的内容');
};

// 发送并立即发送消息
const sendMessage = () => {
  renderParcelRef.current?.sendParentSend('这是要发送的消息');
};

// 通过setHost回调获取的接口调用
let hostInterface: RenderParcelRef | null = null;

const setHost = (host: RenderParcelRef) => {
  hostInterface = host;
};

// 使用hostInterface发送消息
const sendViaHost = () => {
  hostInterface?.sendParentInput('通过host接口发送的内容');
  hostInterface?.sendParentSend('通过host接口发送的消息');
};
```

#### 3. 事件处理最佳实践

**错误处理**:
```typescript
const handleInputContainerResize = (data: { height: number }) => {
  try {
    // 业务逻辑处理
    updateLayout(data.height);
  } catch (error) {
    console.error('处理输入容器尺寸变化失败:', error);
    // 错误恢复逻辑
  }
};
```

**防抖处理**:
```typescript
import { debounce } from 'lodash';

const handleSmartInputChanged = debounce((content: string) => {
  // 防抖处理，避免频繁触发
  performSearch(content);
}, 300);
```

**内存泄漏防护**:
```typescript
useEffect(() => {
  // RenderParcel组件内部已处理事件的订阅和取消订阅
  // 外部页面无需手动管理事件生命周期
}, []);
```

## 迁移指南

### 从postMessage迁移

**原有方式**:
```javascript
// 监听postMessage
window.addEventListener('message', (event) => {
  const { data } = event;
  if (data.type === 'inputContainerResize') {
    handleResize(data.data);
  }
});
```

**新方式**:
```typescript
// 通过props传递处理函数，支持ref调用
<RenderParcel
  ref={renderParcelRef}
  setHost={setHost}
  handleInputContainerResize={handleResize}
  handleBackBtnClicked={handleBackBtnClick}
  // ... 其他props
/>

// 支持外部主动发送消息
renderParcelRef.current?.sendParentInput('外部输入内容');
renderParcelRef.current?.sendParentSend('外部发送消息');
```

### 优势对比

| 特性 | postMessage | 事件总线 |
|------|-------------|----------|
| 类型安全 | ❌ 无类型检查 | ✅ 完整TypeScript支持 |
| 调试友好 | ❌ 难以追踪 | ✅ 清晰的事件流 |
| 错误处理 | ❌ 需手动处理 | ✅ 统一错误边界 |
| 生命周期 | ❌ 需手动管理 | ✅ 自动管理 |
| 维护性 | ❌ 字符串硬编码 | ✅ 类型化事件名 |

## 注意事项

### 1. 事件命名规范
- 所有外部交互事件必须使用 `EXTERNAL_` 前缀
- 使用 `SCREAMING_SNAKE_CASE` 命名风格
- 事件名称应清晰表达其功能和用途

### 2. 数据结构设计
- 保持数据结构简单明确
- 使用TypeScript接口定义数据类型
- 避免传递复杂对象或函数

### 3. 性能考虑
- 对于高频事件（如输入变化），建议使用防抖处理
- 避免在事件处理函数中执行耗时操作
- 合理使用useCallback优化事件处理函数

### 4. 错误处理
- 所有事件处理函数都应包含错误处理逻辑
- 使用try-catch包装业务逻辑
- 提供合理的错误恢复机制

## 扩展指南

### 添加新的外部交互事件

1. **在events.ts中添加事件类型**:
```typescript
export type ExternalInteractionEvents = {
  // 现有事件...
  
  // 新增事件
  EXTERNAL_NEW_EVENT: {
    data: string;
  };
};
```

2. **更新EmitterEvents类型**:
```typescript
type EmitterEvents = {
  // 现有事件...
  
  // 新增事件
  EXTERNAL_NEW_EVENT: ExternalInteractionEvents['EXTERNAL_NEW_EVENT'];
};
```

3. **在RenderParcel.tsx中添加处理逻辑**:
```typescript
// 添加props接口
interface RenderParcelProps {
  // 现有props...
  handleNewEvent: (data: { data: string }) => void;
}

// 添加事件处理函数
const onNewEvent = useCallback((data: { data: string }) => {
  try {
    handleNewEvent(data);
  } catch (error) {
    console.error('处理新事件失败:', error);
  }
}, [handleNewEvent]);

// 在useEffect中订阅事件
useEffect(() => {
  // 现有订阅...
  emitter.on('EXTERNAL_NEW_EVENT', onNewEvent);
  
  return () => {
    // 现有取消订阅...
    emitter.off('EXTERNAL_NEW_EVENT', onNewEvent);
  };
}, [/* 依赖数组 */]);
```

4. **在相应组件中发送事件**:
```typescript
import { emit } from '@/utils/events';

// 在适当的时机发送事件
emit('EXTERNAL_NEW_EVENT', { data: 'example' });
```

## 总结

基于事件总线的IM外部交互方案提供了更好的类型安全、维护性和扩展性。通过统一的事件管理机制，外部页面可以更方便地与IM系统进行交互，同时保持代码的清晰和可维护性。

建议在实际使用中遵循本文档的最佳实践，确保系统的稳定性和性能。
