// clear-logs.js
const fs = require('fs');
const path = require('path');

// 要处理的文件或目录
// 你可以根据需要修改这里的路径
const TARGET_PATH = './src'; // 例如：处理 src 目录下的所有文件

// 正则表达式来匹配 console.debug(...), console.info(...) 和 console.log(...)
// 考虑了多行、各种括号内容和可能的分号结尾
// 使用 | 符号来匹配 "debug", "info" 或 "log"
const REGEX = /console\.(info|log|debug)\s*\([^;]*\);?/g;

function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');

    // 检查文件内容是否包含 console.debug, console.info 或 console.log
    if (content.includes('console.debug') || content.includes('console.info') || content.includes('console.log')) {
      const newContent = content.replace(REGEX, '');
      if (newContent !== content) {
        fs.writeFileSync(filePath, newContent, 'utf8');
        console.log(`Cleaned: ${filePath}`);
      }
    }
  } catch (error) {
    console.error(`Error processing file ${filePath}:`, error);
  }
}

function traverseDirectory(dirPath) {
  try {
    const files = fs.readdirSync(dirPath);

    files.forEach(file => {
      const fullPath = path.join(dirPath, file);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        // 递归处理子目录
        traverseDirectory(fullPath);
      } else if (stat.isFile() && (file.endsWith('.js') || file.endsWith('.ts') || file.endsWith('.jsx') || file.endsWith('.tsx'))) {
        // 只处理 JavaScript/TypeScript 文件
        processFile(fullPath);
      }
    });
  } catch (error) {
    console.error(`Error traversing directory ${dirPath}:`, error);
  }
}

console.log(`Starting to clear console.debug(), console.info() and console.log() calls in ${TARGET_PATH}...`);
const stats = fs.statSync(TARGET_PATH);

if (stats.isDirectory()) {
  traverseDirectory(TARGET_PATH);
} else if (stats.isFile()) {
  processFile(TARGET_PATH);
} else {
  console.error(`Error: ${TARGET_PATH} is not a valid file or directory.`);
}
console.log('Cleaning complete.');