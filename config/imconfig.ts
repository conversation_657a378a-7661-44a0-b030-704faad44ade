
export const AXIOSURL = "http://************/openim/api"

// export const IMWsAddr = (location.host.indexOf('localhost') >-1? `${location.protocol.replace('http','ws')}//***********:10001` : `${location.protocol.replace('http','ws')}//${location.host}/openimws`)
export const IMWsAddr = `${location.protocol.replace('http', 'ws')}//${location.host}/linkFlowService/openimws`
export const IMApiAddr = `${location.protocol}//${location.host}/linkFlowService/openim`

export const platformID = 5

export const coreWasmPath = () => {
    const href = location.href
    const sdkVersion = process?.env?.npm_package_dependencies__ht_openim_wasm_client_sdk || ''

    if (process.env.REACT_APP_ENV === 'sit') return '/PortalStatics/openIM.wasm?version=' + sdkVersion;
    else if (href.indexOf('localhost') >= 0 || href.indexOf('127.0.0.1') >= 0) {
        return '/PortalStatics/openIM.wasm?version=' + sdkVersion;
    } else if (href.indexOf('eipdev') >= 0) {
        return '/PortalStatics/openIM.dev.wasm?version=' + sdkVersion;
    }
    else {
        return '/PortalStatics/openIM.wasm?version=' + sdkVersion;
    }
}

export const sqlWasmPath = `/PortalStatics/sql-wasm.wasm`

//智能助理的用户ID
export const smartAssistantUserID = () => {
    const href = location.href;

    if (href.indexOf('localhost') >= 0 || href.indexOf('127.0.0.1') >= 0 || href.indexOf('eipdev') >= 0) {
        // return '20003137ee3918234cac84789feab4d83d81';
        return '2000ec3e1404d0214974b2afe64fa4f2afdc';
    } else if (href.indexOf('eipsit') >= 0) {
        return '2000ec3e1404d0214974b2afe64fa4f2afdc';
    }
    else if (href.indexOf('eip.htsc.com.cn') >= 0 || href.indexOf('eipnew.htsc.com.cn') >= 0 || href.indexOf('eiplite.htsc.com.cn') >= 0) {
        return '20002b0381ae0fe946b78d4c3f9d20baf83a';
    }
    else return ''
}

//文字转语音的文字
export const ttlUrl = `${location.origin}/htscai/ws3/tts`;

//语音识别url
export const getIatUrl = () => {
    const href = location.href;
    if (href.indexOf('localhost') >= 0 || href.indexOf('127.0.0.1') >= 0) {
        return "ws://************:5068/iat";
    }
    else {
        return `ws://${location.host}/htscai/ws2/iat`;
    }
}