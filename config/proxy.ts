/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 */

export default {
  dev: {
    '/api/': {
      // 使用goapi 进行接口数据定义mock
      // http://goapi.htsc/goapi/web/#/home/<USER>/api?projectId=10002065&moduleid=129331
      target: 'http://mock.htsc/goapi/WEBFE/',
      changeOrigin: true,
    },
    '/PortalStatics/': {
      target: 'http://*************:9999/',
      logLevel: 'debug',
    },
    //用于拿eipsit的token，否则eipsit下的接口调不通
    '/coco-bff/dev/auth/gateway/token/': {
      target: 'http://*************:8082/',
      changeOrigin: true,
      compress: true,
      logLevel: 'debug',
    },
    '/coco-bff/': {
      target: 'http://eipdev.htsc.com.cn/',
      changeOrigin: true,
    },
    '/linkFlowService/openim/department/': {
      target: 'http://openim-api.saassit.htsc.com.cn/',
      // target:"http://eipsit.htsc.com.cn/",
      changeOrigin: true,
      compress: true,
      logLevel: 'debug',
      pathRewrite: { '^/linkFlowService/openim': '' }, // 去掉 /openim 路径
    },
    '/linkFlowService/openim/': {
      target: 'http://openim-api.saassit.htsc.com.cn/',
      changeOrigin: true,
      compress: true,
      logLevel: 'debug',
      pathRewrite: { '^/linkFlowService/openim': '' }, // 去掉 /openim 路径
      onProxyReq: (proxyReq: any, req: any) => {
        proxyReq.setHeader('X-Request-Api', `/linkFlowService/openim`) //为openim静态资源添加请求头
      }
    },
    '/linkFlowService/openimws': {
      target: 'ws://openim-ws.saassit.htsc.com.cn/',
      changeOrigin: true,
      compress: true,
      logLevel: 'debug',
      pathRewrite: { '^/linkFlowService/openimws': '' }, // 去掉 /openim 路径
      ws: true
    },
    '/link-calendar/': {
      target: 'http://*************:8082/',
      changeOrigin: true,
    },
    '/linkflow/bot/interaction/': {
      target: 'http://eipsit.htsc.com.cn/',
      changeOrigin: true,
    },
    "/linkFlowService/openim/avatar/": {
      target: 'http://openim-api.saassit.htsc.com.cn/avatar',
      changeOrigin: true,
      pathRewrite: { '^/linkFlowService': '' }, // 去掉 /openim 路径

    },
    "/htscai/ws3/": {
      target: 'http://************:5066/',
      changeOrigin: true,
      ws: true,
      compress: true,
      pathRewrite: { '^/htscai/ws3': '' }, // 去掉 /openim 路径

      onProxyReq: (proxyReq: any, req: any) => {
        proxyReq.setHeader('X-NLS-Token', `default`) //为openim静态资源添加请求头
      }

      // pathRewrite: { '^/linkFlowService': '' }, // 去掉 /openim 路径
    },
    "/000228-bucket-link-sit-nj/": {
      target: 'http://************:18300/',
      changeOrigin: true,
    },
    '/linq/bff/api/': {
      target: 'http://eipsit.htsc.com.cn/',
      changeOrigin: true,
    }
  },
  sit: {
    '/api/': {
      // 使用goapi 进行接口数据定义mock
      // http://goapi.htsc/goapi/web/#/home/<USER>/api?projectId=10002065&moduleid=129331
      target: 'http://mock.htsc/goapi/WEBFE/',
      changeOrigin: true,
    },
    //dev和sit共用的一个静态资源机器
    '/PortalStatics/': {
      target: 'http://*************:9999/',
      logLevel: 'debug',
    },
    //用于拿eipsit的token，否则eipsit下的接口调不通
    '/coco-bff/dev/auth/gateway/token/': {
      target: 'http://*************:8082/',
      changeOrigin: true,
      compress: true,
      logLevel: 'debug',
    },
    '/coco-bff/': {
      target: 'http://eipsit.htsc.com.cn/',
      changeOrigin: true,
    },
    '/linkFlowService/openim/department/': {
      target: 'http://openim-api.sit.saas.htsc/',
      changeOrigin: true,
      compress: true,
      logLevel: 'debug',
      pathRewrite: { '^/linkFlowService/openim': '' }, // 去掉 /openim 路径
    },
    //不能直接用eipsit的地址，因为wasm里面也是用这个拼，那边发起的请求带不上eip的token
    '/linkFlowService/openim/': {
      target: 'http://openim-api.sit.saas.htsc/',
      changeOrigin: true,
      compress: true,
      logLevel: 'debug',
      pathRewrite: { '^/linkFlowService/openim': '' }, // 去掉 /openim 路径
    },
    '/linkFlowService/openimws': {
      target: 'ws://openim-ws.sit.saas.htsc/',
      changeOrigin: true,
      compress: true,
      logLevel: 'debug',
      pathRewrite: { '^/linkFlowService/openim': '' }, // 去掉 /openim 路径
      ws: true,
    },
    '/link-calendar/': {
      target: 'http://*************:8082/',
      changeOrigin: true,
    },
    '/linkflow/bot/interaction/': {
      target: 'http://eipsit.htsc.com.cn/',
      changeOrigin: true,
    },
    "/linkFlowService/openim/avatar/": {
      target: 'http://eipsit.htsc.com.cn/avatar',
      changeOrigin: true,
      pathRewrite: { '^/linkFlowService': '' }, // 去掉 /openim 路径
    },
    '/linq/bff/api/': {
      target: 'http://eipsit.htsc.com.cn/',
      changeOrigin: true,
    },
    "/htscai/ws3/": {
      target: 'http://************:5066/',
      changeOrigin: true,
      ws: true,
      compress: true,
      pathRewrite: { '^/htscai/ws3': '' }, // 去掉 /openim 路径

      onProxyReq: (proxyReq: any, req: any) => {
        proxyReq.setHeader('X-NLS-Token', `default`) //为openim静态资源添加请求头
      }

      // pathRewrite: { '^/linkFlowService': '' }, // 去掉 /openim 路径
    },
    "/000228-bucket-link-sit-nj/": {
      target: 'http://************:18300/',
      changeOrigin: true,
    },
  }
};
