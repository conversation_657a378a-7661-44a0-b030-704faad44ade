import { useEffect, useState } from 'react';

// 获取虚拟列表容器的高度（外层容器，非里面items的叠加高度）
export function useVirtuosoContainerHeight(id: string, ifShow: boolean) {
  const [height, setHeight] = useState<number | null>(null);

  useEffect(() => {
    let resizeObserver: ResizeObserver | null = null;
    let mutationObserver: MutationObserver | null = null;

    const observe = () => {
      const el = document.getElementById(id);
      if (!el) {
        return;
      }

      // 初始高度
      setHeight(el.clientHeight);

      // 动态监听高度变化
      resizeObserver = new ResizeObserver(() => {
        setHeight(el.clientHeight);
      });
      resizeObserver.observe(el);
    };

    // 先尝试直接获取（有可能已经挂载）
    observe();

    // 如果没拿到，则监听 DOM 树，等 Virtuoso 挂载
    if (!height) {
      mutationObserver = new MutationObserver(() => {
        const el = document.getElementById(id);
        if (el) {
          observe();
          mutationObserver?.disconnect(); // 找到就停止
        }
      });
      mutationObserver.observe(document.body, {
        childList: true,
        subtree: true,
      });
    }

    return () => {
      resizeObserver?.disconnect();
      mutationObserver?.disconnect();
    };
  }, [ifShow, id, height]);

  return height;
}
