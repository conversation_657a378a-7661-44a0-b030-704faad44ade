import {
  ConversationItem,
  MessageItem,
  MessageReceiveOptType,
  MessageStatus,
  MessageType,
} from '@ht/openim-wasm-client-sdk';
import { Modal } from '@ht/sprite-ui';
import { t } from 'i18next';
import { useCallback, useEffect, useRef, useState } from 'react';

import { IMSDK } from '@/layouts/BasicLayout';
import { clearMessages } from '@/hooks/useHistoryMessageList';
import { feedbackToast } from '@/utils/common';
import { useConversationStore, useUserStore } from '@/store';
import { isBotUser } from '@/utils/avatar';

export function useConversationSettings(conversation?: ConversationItem) {
  const selfID = useUserStore.getState().selfInfo.userID;
  const delConversationByCID = useConversationStore(
    (state) => state.delConversationByCID
  );

  const updateCurrentConversation = useConversationStore(
    (state) => state.updateCurrentConversation
  );

  const [currentMemberIfJoinedGroup, setCurrentMemberIfJoinedGroup] =
    useState<boolean>(false);
  const { userID: selfUserID } = useUserStore.getState().selfInfo;
  const [currentMemberIsGroupOwner, setCurrentMemberIsGroupOwner] =
    useState<boolean>(false);

  useEffect(() => {
    if (conversation?.groupID) {
      IMSDK.isJoinGroup(conversation?.groupID).then((res) => {
        const { data } = res;
        setCurrentMemberIfJoinedGroup(data);
      });
      IMSDK.getSpecifiedGroupsInfo([conversation.groupID]).then((res) => {
        const { data } = res;
        if (data[0]?.creatorUserID === selfUserID) {
          setCurrentMemberIsGroupOwner(true);
        } else {
          setCurrentMemberIsGroupOwner(false);
        }
      });
    }
  }, [conversation?.groupID, selfUserID]);

  const updateConversationPin = useCallback(
    async (isPinned: boolean) => {
      if (!conversation) {
        return;
      }

      try {
        await IMSDK.setConversation({
          conversationID: conversation.conversationID,
          isPinned,
        });
      } catch (error) {
        feedbackToast({ error, msg: t('toast.pinConversationFailed') });
      }
    },
    [conversation]
  );

  const updateConversationMessageRemind = useCallback(
    async (checked: boolean, option?: MessageReceiveOptType) => {
      if (!conversation) {
        return;
      }

      try {
        await IMSDK.setConversation({
          conversationID: conversation.conversationID,
          recvMsgOpt: checked ? option : MessageReceiveOptType.Normal,
        });
      } catch (error) {
        feedbackToast({
          error,
          msg: t('toast.setConversationRecvMessageOptFailed'),
        });
      }
    },
    [conversation]
  );

  const hideConversation = useCallback(async () => {
    if (!conversation) {
      return;
    }

    try {
      await IMSDK.hideConversation(conversation.conversationID);
    } catch (error) {
      console.error('隐藏会话失败', conversation.conversationID);
      feedbackToast({
        error,
        msg: t('toast.hideConversationFailed'),
      });
    }
  }, [conversation]);

  const handleConversationRemove = async () => {
    try {
      if (conversation?.conversationID != null) {
        delConversationByCID(conversation?.conversationID);
        updateCurrentConversation();
        IMSDK.hideConversation(conversation?.conversationID);
      }
    } catch (error) {
      console.error('hideConversation', error);
    }
  };

  const clearConversationMessages = () => {
    if (!conversation) {
      return;
    }
    Modal.confirm({
      title: t('toast.clearChatHistory'),
      content: t('toast.confirmClearChatHistory'),
      onOk: async () => {
        try {
          await IMSDK.clearConversationAndDeleteAllMsg(
            conversation.conversationID
          );
          clearMessages();
        } catch (error) {
          feedbackToast({
            error,
            msg: t('toast.clearConversationMessagesFailed'),
          });
        }
      },
    });
  };

  const isMsgNeedMarkAsRead = useCallback(
    (msgItem: MessageItem) => {
      if (msgItem.sendID === selfID) {
        return false;
      } // 自己发的消息不用标
      if (msgItem.isRead) {
        return false;
      } // 已标为已读的不用标
      if (isBotUser(msgItem.sendID)) {
        return false;
      } // 机器人消息不用标已读

      if (
        msgItem.status === MessageStatus.Failed ||
        msgItem.status === MessageStatus.Sending
      ) {
        return false;
      } // 发送中和失败的不用标记，这个按理说只有自己发的消息会存在，也过滤下吧

      if (
        msgItem.contentType === MessageType.GroupCreated ||
        msgItem.contentType === MessageType.MemberInvited ||
        msgItem.contentType === MessageType.MemberEnter ||
        msgItem.contentType === MessageType.MemberKicked ||
        msgItem.contentType === MessageType.GroupDismissed ||
        msgItem.contentType === MessageType.MemberQuit ||
        msgItem.contentType === MessageType.GroupNameUpdated
      ) {
        return false;
      } // 一些通知类消息，不需要展示已读未读的，不用标记

      return true;
    },
    [selfID]
  );

  // 消息标已读
  // const markConversationMessageAsRead = useCallback(
  //   (realMessage: MessageItem) => {
  //     if (!isMsgNeedMarkAsRead(realMessage)) {
  //       return;
  //     }
  //     if (realMessage != null && conversation?.conversationID) {
  //       if (conversation.groupID != null && conversation.groupID !== '') {
  //         IMSDK.markGroupMessageRead({
  //           conversationID: conversation?.conversationID,
  //           clientMsgIDList: [realMessage.clientMsgID],
  //         });
  //       } else {
  //         IMSDK.markMessagesAsReadByMsgID({
  //           conversationID: conversation?.conversationID,
  //           clientMsgIDList: [realMessage.clientMsgID],
  //         });
  //       }
  //     }
  //   },
  //   [conversation?.conversationID, conversation?.groupID, isMsgNeedMarkAsRead]
  // );

  // // 消息批量标已读
  // const markConversationMessagesAsRead = useCallback(
  //   (clientMsgIDs: string[]) => {
  //
  //     if (
  //       clientMsgIDs == null ||
  //       isEmpty(clientMsgIDs) ||
  //       conversation?.conversationID == null
  //     ) {
  //       return;
  //     }

  //     // 群聊和单聊的标记已读调用不同的接口
  //     if (conversation.groupID != null && conversation.groupID !== '') {
  //       IMSDK.markGroupMessageRead({
  //         conversationID: conversation?.conversationID,
  //         clientMsgIDList: clientMsgIDs,
  //       });

  //
  //     } else {
  //       IMSDK.markMessagesAsReadByMsgID({
  //         conversationID: conversation?.conversationID,
  //         clientMsgIDList: clientMsgIDs,
  //       });
  //     }
  //   },
  //   [conversation?.conversationID, conversation?.groupID]
  // );

  const messageQueue = useRef<Set<string>>(new Set());
  const flushTimer = useRef<NodeJS.Timeout | null>(null);

  const flush = useCallback(() => {
    if (!conversation?.conversationID || messageQueue.current.size === 0) {
      return;
    }

    const clientMsgIDList = Array.from(messageQueue.current);
    messageQueue.current.clear();

    if (flushTimer.current) {
      clearTimeout(flushTimer.current);
      flushTimer.current = null;
    }

    const payload = {
      conversationID: conversation.conversationID,
      clientMsgIDList,
    };

    if (conversation.groupID) {
      IMSDK.markGroupMessageRead(payload);
    } else {
      IMSDK.markMessagesAsReadByMsgID(payload);
    }
  }, [conversation]);

  const markConversationMessageAsRead = useCallback(
    (msg: MessageItem) => {
      if (!conversation?.conversationID) {
        return;
      }
      if (!isMsgNeedMarkAsRead(msg)) {
        return;
      }

      messageQueue.current.add(msg.clientMsgID);

      if (!flushTimer.current) {
        flushTimer.current = setTimeout(flush, 500);
      }
    },
    [conversation?.conversationID, flush, isMsgNeedMarkAsRead]
  );

  return {
    hideConversation,
    updateConversationPin,
    updateConversationMessageRemind,
    clearConversationMessages,
    handleConversationRemove,
    // markAsRead,
    markConversationMessageAsRead,
    // markConversationMessagesAsRead,
    currentMemberIfJoinedGroup,
    currentMemberIsGroupOwner,
  };
}
