import { useCallback, useRef, useState } from 'react';
import { MessageItem, ConversationItem } from '@ht/openim-wasm-client-sdk';
import { debounce } from 'lodash';
import { VirtuosoHandle } from '@ht/react-virtuoso';

interface UseMessageScrollProps {
  virtuosoRef: React.RefObject<VirtuosoHandle>;
  conversation?: ConversationItem;
  onMarkAsRead?: () => void;
}

export const useMessageScroll = ({
  virtuosoRef,
  conversation,
  onMarkAsRead,
}: UseMessageScrollProps) => {
  const [hasScrolled, setHasScrolled] = useState(false);
  const prevListLengthRef = useRef<number>(0);

  // 初始化时立即滚动到底部
  const scrollToBottomImmediate = useCallback(() => {
    virtuosoRef.current?.scrollToIndex({
      index: 'LAST',
      align: 'end',
      behavior: 'auto',
    });
    onMarkAsRead?.();
  }, [onMarkAsRead, virtuosoRef]);
  // }, [virtuosoRef]);
  const scrollToBottom = useCallback(() => {
    virtuosoRef.current?.scrollToIndex({
      index: 'LAST',
      align: 'end',
      behavior: 'smooth',
    });
    onMarkAsRead?.();
  }, [onMarkAsRead, virtuosoRef]);

  // 平滑滚动到底部
  const scrollToBottomSmooth = useCallback(
    (immediate = false) => {
      const executeScroll = () => {
        let count = 0;

        // 如果需要立即执行一次
        if (immediate) {
          scrollToBottom();
          count++;
        }

        const interval = setInterval(() => {
          scrollToBottom();
          count++;
          if (count >= 2) {
            // 固定执行 2 次
            clearInterval(interval);
          }
        }, 500); // 固定 500ms
      };

      onMarkAsRead?.();

      if (document.visibilityState === 'hidden') {
        const handleVisibilityChange = () => {
          if (document.visibilityState === 'visible') {
            executeScroll();
            document.removeEventListener(
              'visibilitychange',
              handleVisibilityChange
            );
          }
        };
        document.addEventListener('visibilitychange', handleVisibilityChange);
      } else {
        executeScroll();
      }
    },
    [onMarkAsRead, scrollToBottom]
  );

  // }, [virtuosoRef]);

  // 处理消息列表变化
  const prevLastMsgIdRef = useRef<string | null>(null);

  const handleMessagesChange = useCallback(
    (messages: MessageItem[], isSelfMessage = false) => {
      if (!messages.length) {
        return;
      }

      const lengthDiff = messages.length - prevListLengthRef.current;
      const lastMsgId = messages[messages.length - 1]?.clientMsgID; // 或其他唯一字段
      const lastMsgChanged = lastMsgId !== prevLastMsgIdRef.current;

      // 仅当新增的消息在尾部（lastMsgId 变化）时，才认为是新消息
      if (
        lengthDiff > 0 &&
        lengthDiff <= 2 &&
        lastMsgChanged &&
        (!hasScrolled || isSelfMessage)
      ) {
        setTimeout(scrollToBottom, 30);
      }

      prevListLengthRef.current = messages.length;
      prevLastMsgIdRef.current = lastMsgId;
    },
    [hasScrolled, scrollToBottom]
  );

  // 接收到新消息时，会立刻触发一次不触底，scrollToBottom后再触发滚到底，导致button会闪现，因此这里加一个debounce
  const updateScrollState = useCallback(
    debounce((atBottomState: boolean) => {
      setHasScrolled(!atBottomState);
    }, 500),
    []
  );

  const handleNearBottom = useCallback(
    (atBottomState) => {
      updateScrollState(atBottomState);
      if (atBottomState) {
        onMarkAsRead?.();
      }
    },
    [updateScrollState, onMarkAsRead]
  );

  return {
    hasScrolled,
    scrollToBottomImmediate,
    scrollToBottomSmooth,
    handleMessagesChange,
    handleNearBottom,
    scrollToBottom,
  };
};
