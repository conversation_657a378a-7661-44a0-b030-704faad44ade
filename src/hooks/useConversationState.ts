import { useLatest, useThrottleFn, useUpdateEffect } from 'ahooks';
import { useEffect } from 'react';

import { useCurrentMemberRole } from '@/hooks/useCurrentMemberRole';
import { IMSDK } from '@/layouts/BasicLayout';
import { useContactStore, useConversationStore, useUserStore } from '@/store';
import { ConversationItem, GroupAtType } from '@ht/openim-wasm-client-sdk';
import { cloneDeep } from 'lodash';
import { platform } from '@tauri-apps/plugin-os';
import { getCurrentWindow } from '@tauri-apps/api/window';

export default function useConversationState(conversation?: ConversationItem) {
  const syncState = useUserStore((state) => state.syncState);
  const latestSyncState = useLatest(syncState);
  const currentConversation = cloneDeep(conversation);

  const updateCurrentConversation = useConversationStore(
    (state) => state.updateCurrentConversation
  );

  const latestCurrentConversation = useLatest(currentConversation);
  const isBlackUser = useContactStore(
    (state) =>
      state.blackList.findIndex(
        (user) => user.userID === currentConversation?.userID
      ) !== -1
  );

  const { isJoinGroup, isNomal } = useCurrentMemberRole();

  useUpdateEffect(() => {
    if (syncState !== 'loading') {
      checkConversationState();
    }
  }, [syncState]);

  // useUpdateEffect(() => {
  //   throttleCheckConversationState();
  // }, [currentConversation?.unreadCount]);

  useEffect(() => {
    checkConversationState();
  }, [currentConversation?.conversationID]);

  // useEffect(() => {
  //   function handleVisibilityChange() {
  //     if (document.visibilityState === 'visible') {
  //       checkConversationState();
  //     }
  //   }
  //   document.addEventListener('visibilitychange', handleVisibilityChange);
  // }, []);

  const checkConversationState = async () => {
    if (
      !latestCurrentConversation.current ||
      latestSyncState.current === 'loading' ||
      document.visibilityState === 'hidden'
    ) {
      return;
    }

    if (latestCurrentConversation.current.unreadCount > 0) {
      await IMSDK.markConversationMessageAsRead(
        latestCurrentConversation.current.conversationID
      );
      const { unReadCount } = useConversationStore.getState();
      if (window.__TAURI_INTERNALS__) {
        const currentPlatform = platform();
        if (currentPlatform === 'macos') {
          const { unReadCount } = useConversationStore.getState();
          getCurrentWindow().setBadgeCount(
            unReadCount === 0 ? undefined : unReadCount + 1
          );
        }
      }
    }
    const { groupAtType, groupTag = 0 } = latestCurrentConversation.current;

    if (
      groupAtType === GroupAtType.AtAllAtMe ||
      groupAtType === GroupAtType.AtMe ||
      groupAtType === GroupAtType.AtAll
    ) {
      IMSDK.setConversation({
        conversationID: latestCurrentConversation.current.conversationID,
        groupAtType: 0,
      });
    }
    if (groupTag === 1) {
      IMSDK.setConversation({
        conversationID: latestCurrentConversation.current.conversationID,
        groupTag: 0,
      });
    }
  };

  const { run: throttleCheckConversationState } = useThrottleFn(
    checkConversationState,
    { wait: 2000, leading: true }
  );

  const getIsCanSendMessage = () => {
    if (currentConversation?.userID) {
      return !isBlackUser;
    }

    if (!isJoinGroup) {
      return false;
    }

    return true;
  };

  return {
    getIsCanSendMessage,
    isBlackUser,
    currentConversation,
    checkConversationState,
    throttleCheckConversationState,
  };
}
