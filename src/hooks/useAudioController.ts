import { AudioState, audioController } from '@/controller/AudioController';
import { useEffect, useState } from 'react';

export function useAudioController() {
  const [state, setState] = useState<AudioState>(audioController.state);
  const [currentId, setCurrentId] = useState<string>(audioController.currentId);

  useEffect(() => {
    const handler = (newState: AudioState, id?: string) => {
      setState(newState);
      if (id !== undefined) {
        setCurrentId(id);
      }
    };
    audioController.on('stateChange', handler);
    return () => {
      audioController.off('stateChange', handler);
    };
  }, []);

  return {
    state,
    currentId,
    play: (id: string, text: string) => audioController.play(id, text),
    stop: () => audioController.stop(),
  };
}
