/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable indent */
/* eslint-disable max-statements */
// CodeMirror imports
import { EditorView } from '@codemirror/view';
import { EditorState } from '@codemirror/state';
import { githubLight } from '@fsegurai/codemirror-theme-github-light';
import { commandsCtx } from '@milkdown/core';
import { expectDomTypeError } from '@milkdown/exception';
import { setBlockType } from '@milkdown/prose/commands';
import { textblockTypeInputRule } from '@milkdown/prose/inputrules';
import {
  $command,
  $inputRule,
  $nodeAttr,
  $nodeSchema,
  $useKeymap,
} from '@milkdown/utils';
import { withMeta } from './__internal__';
import { languageMap } from '../components/code-block/view/loader';

export const codeBlockAttr = $nodeAttr('codeBlock', () => ({
  pre: {},
  code: {},
}));

withMeta(codeBlockAttr, {
  displayName: 'Attr<codeBlock>',
  group: 'CodeBlock',
});

/// Schema for code block node.
export const codeBlockSchema = $nodeSchema('code_block', (ctx) => {
  return {
    content: 'text*',
    group: 'block',
    marks: '',
    defining: true,
    code: true,
    attrs: {
      language: {
        default: '',
      },
    },
    parseDOM: [
      {
        tag: 'pre',
        preserveWhitespace: 'full',
        getAttrs: (dom) => {
          if (!(dom instanceof HTMLElement)) {
            throw expectDomTypeError(dom);
          }

          return { language: dom.dataset.language };
        },
      },
    ],
    // toDOM: (node) => {
    //   const attr = ctx.get(codeBlockAttr.key)(node);
    //   return [
    //     'pre',
    //     {
    //       ...attr.pre,
    //       'data-language': node.attrs.language,
    //     },
    //     ['code', attr.code, 0],
    //   ];
    // },
    toDOM: (node) => {
      const attr = ctx.get(codeBlockAttr.key)(node);
      const language = node.attrs.language || 'text';

      // 创建复制按钮的函数
      const createCopyButton = (code: string) => {
        const copyButton = document.createElement('button');
        copyButton.textContent = '复制';
        copyButton.className = 'copy-button-click';
        copyButton.style.border = 'none';
        copyButton.style.background = 'transparent';
        copyButton.style.cursor = 'pointer';
        copyButton.style.color = 'var(--primary-text-color)';
        copyButton.style.fontSize = '12px';
        copyButton.style.padding = '4px 8px';
        copyButton.style.borderRadius = '4px';
        copyButton.title = '复制代码';

        // 创建隐藏的pre元素，包含code标签和原始代码
        const hiddenPre = document.createElement('pre');
        hiddenPre.style.display = 'none';
        // 添加属性，参考用户要求的格式
        Object.assign(hiddenPre, attr.pre);
        hiddenPre.setAttribute('data-language', language || 'text');

        const codeElement = document.createElement('code');
        // 添加属性，参考用户要求的格式
        Object.assign(codeElement, attr.code);
        codeElement.textContent = code;

        hiddenPre.appendChild(codeElement);
        copyButton.appendChild(hiddenPre);

        return copyButton;
      };

      // 封装创建代码块头部的函数
      const createHeader = (language: string, code: string) => {
        const header = document.createElement('div');
        header.className = 'code-header';
        header.style.backgroundColor = 'var(--primary-background-color-16)';
        header.style.height = '38px';
        header.style.display = 'flex';
        header.style.alignItems = 'center';
        header.style.justifyContent = 'space-between';
        header.style.padding = '0 10px';
        header.style.border = '1px solid var(--primary-background-color-5)';
        header.style.borderRadius = '8px 8px 0px 0px';

        // 创建左侧容器
        const leftContainer = document.createElement('div');
        leftContainer.style.display = 'flex';
        leftContainer.style.alignItems = 'center';

        // 创建图标元素
        const icon = document.createElement('img');
        icon.src = new URL('@/assets/md/daimayuyan.png', import.meta.url).href;
        icon.className = 'code-language-icon';

        // 创建语言文本元素
        const langText = document.createElement('span');
        langText.textContent =
          language === 'text' ? 'Plain Text' : language.toLowerCase();

        // 将图标和文本添加到左侧容器中
        leftContainer.appendChild(icon);
        leftContainer.appendChild(langText);

        // 创建复制按钮并添加到header中
        const copyButton = createCopyButton(code);

        // 将左侧容器和复制按钮添加到header中
        header.appendChild(leftContainer);
        header.appendChild(copyButton);

        return header;
      };

      // 封装创建CodeMirror容器的函数
      const createCodeMirrorContainer = (code: string, language: string) => {
        const cmContainer = document.createElement('div');
        cmContainer.className = 'cm-container';
        cmContainer.style.background = '#FAFAFA';
        cmContainer.style.borderRadius = '0px 0px 8px 8px';
        cmContainer.style.border = '1px solid #DDDEE0';

        // 创建编辑器实例并存储到容器的引用中
        const view = new EditorView({
          // 移除 doc 属性，因为它可能存在问题
          // doc: code,
          root: document,
          extensions: [
            EditorState.readOnly.of(true),
            EditorView.editable.of(false),
            githubLight,
            // 同步获取语言支持
            languageMap[language.toLowerCase()] || [],
          ],
          parent: cmContainer,
        });

        // 使用 dispatch 确保内容被正确地插入到文档中
        view.dispatch({
          changes: { from: 0, to: view.state.doc.length, insert: code },
        });

        // 保存编辑器实例到容器的属性中，方便后续访问和清理
        (cmContainer as any)._editorView = view;

        return cmContainer;
      };

      // 获取代码内容
      const code = node.textContent || '';

      // 创建pre元素
      const wrapper = document.createElement('div');
      Object.assign(wrapper, attr.pre);
      wrapper.setAttribute('data-language', node.attrs.language);
      wrapper.appendChild(createHeader(language, code));
      wrapper.appendChild(createCodeMirrorContainer(code, language));
      wrapper.className = 'code-block-formate-wrapper';
      return wrapper;
    },
    parseMarkdown: {
      match: ({ type }) => type === 'code',
      runner: (state, node, type) => {
        const language = node.lang as string;
        const value = node.value as string;
        state.openNode(type, { language });
        if (value) {
          state.addText(value);
        }

        state.closeNode();
      },
    },
    toMarkdown: {
      match: (node) => node.type.name === 'code_block',
      runner: (state, node) => {
        state.addNode('code', undefined, node.content.firstChild?.text || '', {
          lang: node.attrs.language,
        });
      },
    },
  };
});

withMeta(codeBlockSchema.node, {
  displayName: 'NodeSchema<codeBlock>',
  group: 'CodeBlock',
});

withMeta(codeBlockSchema.ctx, {
  displayName: 'NodeSchemaCtx<codeBlock>',
  group: 'CodeBlock',
});

/// A input rule for creating code block.
/// For example, ` ```javascript ` will create a code block with language javascript.
export const createCodeBlockInputRule = $inputRule((ctx) =>
  textblockTypeInputRule(
    /^```(?<language>[a-z]*)?[\s\n]$/,
    codeBlockSchema.type(ctx),
    (match) => ({
      language: match.groups?.language ?? '',
    })
  )
);

withMeta(createCodeBlockInputRule, {
  displayName: 'InputRule<createCodeBlockInputRule>',
  group: 'CodeBlock',
});

/// A command for creating code block.
/// You can pass the language of the code block as the parameter.
export const createCodeBlockCommand = $command(
  'CreateCodeBlock',
  (ctx) =>
    (language = '') =>
      setBlockType(codeBlockSchema.type(ctx), { language })
);

withMeta(createCodeBlockCommand, {
  displayName: 'Command<createCodeBlockCommand>',
  group: 'CodeBlock',
});

/// A command for updating the code block language of the target position.
export const updateCodeBlockLanguageCommand = $command(
  'UpdateCodeBlockLanguage',
  () =>
    (
      { pos, language }: { pos: number; language: string } = {
        pos: -1,
        language: '',
      }
    ) =>
    (state, dispatch) => {
      if (pos >= 0) {
        dispatch?.(state.tr.setNodeAttribute(pos, 'language', language));
        return true;
      }

      return false;
    }
);

withMeta(updateCodeBlockLanguageCommand, {
  displayName: 'Command<updateCodeBlockLanguageCommand>',
  group: 'CodeBlock',
});

/// Keymap for code block.
/// - `Mod-Alt-c`: Create a code block.
export const codeBlockKeymap = $useKeymap('codeBlockKeymap', {
  CreateCodeBlock: {
    shortcuts: 'Mod-Alt-c',
    command: (ctx) => {
      const commands = ctx.get(commandsCtx);
      return () => commands.call(createCodeBlockCommand.key);
    },
  },
});

withMeta(codeBlockKeymap.ctx, {
  displayName: 'KeymapCtx<codeBlock>',
  group: 'CodeBlock',
});

withMeta(codeBlockKeymap.shortcuts, {
  displayName: 'Keymap<codeBlock>',
  group: 'CodeBlock',
});
