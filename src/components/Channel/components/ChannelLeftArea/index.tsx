import { memo, FC } from 'react';
import { useConversationStore } from '@/store';
import { isEmpty } from 'lodash';
import ChannelHeader from '../ChannelHeader';
import ChannelArea from '../ChannelArea';

import styles from './index.less';

import EmptyChannel from '../EmptyChannel';

export interface LeftAreaProps {
  hasDeleteIcon?: boolean;
}

const ChannelLeftArea: FC<LeftAreaProps> = (props) => {
  const { hasDeleteIcon = false } = props;

  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );

  return (
    <>
      {!isEmpty(currentConversation?.conversationID) ? (
        <div className={styles.channelContent}>
          <ChannelHeader hasDeleteIcon={hasDeleteIcon} />
          <ChannelArea />
        </div>
      ) : (
        <EmptyChannel />
      )}
    </>
  );
};

export default memo(ChannelLeftArea);
