.actionButton {
  background: none;
  border: none;
  box-shadow: none;
  color: var(--primary-text-color-3);
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;

  &.inSmart {
    height: 16px;
    width: 16px;

    > img {
      width: 100% !important;
      height: 100% !important;
    }
  }
  &:hover {
    border-radius: 4px;
    background: var(--msg-qute-backgroud-color);
  }
}

.operateItem {
  display: flex;
  cursor: pointer;
  img {
    margin-right: 6px;
  }
}

.loadingIcon {
  font-size: 14px;
  color: var(--primary-text-color-3);
}

.divider {
  width: 1px;
  height: 12px;
  background: #ebebeb;
  margin: 0 4px;
}

.popOverContainer {
  :global {
    .linkflow-popover-content {
      width: 120px;
      height: 48px;
      background: #ffffff;
      box-shadow: 0 2px 12px 0 rgba(13, 26, 38, 12%);
      border-radius: 8px;
    }

    .linkflow-popover-inner-content {
      // padding: 0 !important;
      border: 0;
    }

    .linkflow-popover-inner {
      background-color: transparent;
      box-shadow: none;
    }
  }
}
