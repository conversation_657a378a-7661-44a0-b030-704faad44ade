import React, { ReactNode, useState } from 'react';
import { Space, Tooltip } from '@ht/sprite-ui';
import { MessageItem as MessageItemType } from '@ht/openim-wasm-client-sdk';
import { isInSmartAssitantantPage } from '@/utils/utils';
import classNames from 'classnames';
import { IMSDK } from '@/layouts/BasicLayout';
import { deleteOneMessage } from '@/hooks/useHistoryMessageList';
import { feedbackToast } from '@/utils/common';
import { useConversationStore } from '@/store';
import styles from './index.less';
import DeleteConfirmModal from '../DeleteConfirmModal';

interface ActionButtonProps {
  message: MessageItemType;
  onRefresh?: () => void;
  onFeedback?: (type: 'like' | 'dislike') => void;
  buttons: Array<{
    key: string;
    title: string;
    icon: ReactNode;
    onClick?: () => void;
    loading?: boolean;
    active?: boolean;
  }>;
  conversationID?: string;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  message,
  onRefresh,
  onFeedback,
  buttons: customButtons,
  conversationID,
}) => {
  const buttons = customButtons;

  const isMultiSession = useConversationStore((state) => state.isMultiSession);
  const currentMultiSession = useConversationStore(
    (state) => state.currentMultiSession
  );
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );

  const useConversation = isMultiSession
    ? currentMultiSession
    : currentConversation;

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const tryRemove = async () => {
    try {
      if (useConversation?.conversationID != null) {
        await IMSDK.deleteMessage({
          clientMsgID: message.clientMsgID,
          conversationID: useConversation?.conversationID,
        });

        deleteOneMessage(message.clientMsgID);
      }
    } catch (error) {
      console.error({ message, error });
      feedbackToast({ error, msg: '删除失败' });
    }
  };

  return (
    <>
      <Space size={isInSmartAssitantantPage() ? 8 : 2}>
        {buttons.map(({ key, title, icon, onClick, loading }) => {
          return (
            <Tooltip title={title} key={key}>
              <div
                onClick={() => {
                  if (onClick) {
                    onClick();
                  } else if (key === 'delete') {
                    setShowDeleteModal(true);
                  }
                }}
                className={classNames(
                  styles.actionButton,
                  isInSmartAssitantantPage() ? styles.inSmart : ''
                )}
              >
                {icon}
              </div>
            </Tooltip>
          );
        })}
      </Space>
      {showDeleteModal && (
        <DeleteConfirmModal
          isOpen={showDeleteModal}
          handleCancel={() => setShowDeleteModal(false)}
          onDelete={() => tryRemove()}
        />
      )}
    </>
  );
};

export default ActionButton;
