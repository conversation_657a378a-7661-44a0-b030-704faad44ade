/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable complexity */
/* eslint-disable max-statements */
/* eslint-disable max-lines */
/* eslint-disable indent */
import { FC, useRef, useEffect, useState } from 'react';
import classNames from 'classnames';
import Mark from 'mark.js';
import { RenderMd } from '@/components/MdEditor';
import {
  useConversationStore,
  useSmartAssitantStore,
  useUserStore,
} from '@/store';
import { Space } from '@ht/sprite-ui';
import type { StepsProps } from '@ht/sprite-ui';
import { ConsoleSqlOutlined, LoadingOutlined } from '@ht-icons/sprite-ui-react';
import useUserInfo from '@/hooks/useUserInfo';
import { useSendMessage } from '@/hooks/useSendMessage';
import copyIcon from '@/assets/stream/copy.png';
import refreshIcon from '@/assets/stream/refresh.png';
import dislikeHollow from '@/assets/channel/messageRender/dislikeHollow.png';
import dislikeSolid from '@/assets/channel/messageRender/dislikeSolid.png';
import likeHollow from '@/assets/channel/messageRender/likeHollow.png';
import likeSolid from '@/assets/channel/messageRender/likeSolid.png';
import check from '@/assets/stream/check.svg';
import { handleCopy } from '@/utils/message';
import { AILoadingText } from '@/utils/constants';
import { useTranslationStore } from '@/store/translationStore';
import { IMSDK } from '@/layouts/BasicLayout';
import readIconInSmart from '@/assets/smartAssistant/readIcon.svg';
import deleteIconInSmart from '@/assets/smartAssistant/deleteIcon.svg';
import copyIconInSmart from '@/assets/smartAssistant/copyIcon.svg';
import redoIconInSmart from '@/assets/smartAssistant/redoIcon.svg';
import expandIcon from '@/assets/smartAssistant/expandIcon.svg';
import { useAudioController } from '@/hooks/useAudioController';
import { isInSmartAssitantantPage } from '@/utils/utils';
import { MessageItem, MessageType } from '@ht/openim-wasm-client-sdk';
import { feedbackToast } from '@/utils/common';
import TranslationRender from '../TranslationRender';
import { IMessageItemProps } from '..';
import NetworkContent from './NetworkContent';
import KnowledgeContent from './KnowledgeContent';
import ActionButtons from './ActionButton';
import Loading from './Loading';
import Cost from './Cost';
import styles from './index.less';
import ThinkContent from './ThinkContent';
import WaveBars from './WaveBar';
import { highlightPseudoElement } from '../TextMessageRender';

interface StreamMessageProps extends IMessageItemProps {
  content: {
    content: ContentType;
    event?: string;
  };
}

interface ContentType {
  id: string;
  start: number;
  end: number;
  answer?: string;
  input_tokens: number;
  output_tokens: number;
  latency: number;
  think?: {
    answer: string;
  };
  knowledge_retrieve?: {
    start: number;
    end: number;
    latency: number;
    results: Array<{
      index: number;
      score: number;
      doc_name: string;
      doc_type: number;
      doc_url: string;
      content: string;
    }>;
  };
  qa_retrieve?: {
    start: number;
    end: number;
    latency: number;
    results: Array<{
      index: number;
      score: number;
      question: string;
      answer: string;
    }>;
  };
  network_search?: {
    start: number;
    end: number;
    latency: number;
    results: Array<{
      index: number;
      title: string;
      url: string;
      content: string;
    }>;
  };
  error?: {
    code: string;
    msg: string;
  };
  feedback?: number;
  plugins: PluginCallProps[];
}

interface PluginCallProps {
  clientMsgId: string;
  conversation_id: string;
  created_at: number;
  event: string;
  index: number;
  latency?: number;
  name: string;
  operation_id: string;
  status?: number;
  trace_id: string;
  start?: number;
  end?: number;
  answer?: string;
}

/**
 * 处理消息内容中的图片URL替换
 * @param content 原始消息内容
 * @returns 处理后的消息内容
 */
export const processImageUrls = (content: string): string => {
  if (typeof content !== 'string') {
    return content;
  }
  // 测试环境将eiplite替换为eipsit
  if (location.host === 'eipsit.htsc.com.cn') {
    return content
      .replace(/eiplite\.htsc\.com\.cn\/llmpf/g, 'eipsit.htsc.com.cn/llmops')
      .replace(
        /eiplite\.htsc\.com\.cn\/llmproxy/g,
        'eipsit.htsc.com.cn/llmproxy'
      );
  }
  // 生产环境eipnew替换为当前域名
  else if (['eip.htsc.com.cn', 'eiplite.htsc.com.cn'].includes(location.host)) {
    return content
      .replace(/eipnew\.htsc\.com\.cn\/llmpf/g, `${location.host}/llmpf`)
      .replace(/eipnew\.htsc\.com\.cn\/llmproxy/g, `${location.host}/llmproxy`);
  }
  return content;
};

const customDot: StepsProps['progressDot'] = (dot, { status, index }) => {
  if (status === 'finish') {
    return (
      <img
        style={{
          width: '18px',
          height: '18px',
          position: 'relative',
          top: '-7px',
          left: '-5px',
        }}
        src={check}
      />
    );
  } else if (status === 'wait') {
    return <LoadingOutlined />;
  }
};
const StreamMessageRender: FC<StreamMessageProps> = (props) => {
  const {
    content,
    message,
    prevMsg,
    isLastMsg,
    isSearch,
    conversationID,
    searchValue = '',
    isForwardMessage,
    inMultiMessageModal,
    inHistoryList,
  } = props;

  const {
    state: readingStatus,
    currentId,
    play: playAudio,
    stop: stopAudio,
  } = useAudioController();

  const { userID } = useUserStore.getState().selfInfo;

  const handleRead = (message: MessageItem) => {
    if (message.contentType === MessageType.CustomMessage) {
      let content: any = {};
      try {
        content = JSON.parse(message?.customElem?.data || '{}');
      } catch (e) {
        content = message?.customElem?.data;
      }
      if (content?.type === 'stream' && content?.content?.answer) {
        if (currentId === message.clientMsgID && readingStatus !== 'idle') {
          stopAudio();
        } else {
          playAudio(message.clientMsgID, content?.content?.answer);
        }
      }
    }
  };

  // 获取按钮配置的函数
  const getActionButtons = ({
    messageContent,
    message,
    conversationID,
    isLastMsg,
    handleRefresh,
    isError = false,
  }: {
    messageContent: ContentType;
    message: any;
    conversationID: string;
    handleRefresh: () => void;
    isError?: boolean;
    isLastMsg?: boolean;
  }) => {
    const reactionStatus = {
      like:
        message.attachedInfoElem.reaction?.logs?.findIndex(
          (reaction: any) => reaction.emoji === '👍'
        ) >= 0,
      dislike:
        message.attachedInfoElem.reaction?.logs?.findIndex(
          (reaction: any) => reaction.emoji === '👎'
        ) >= 0,
    };
    const likeDislikeBtns: any = [
      {
        key: 'like',
        title: '点赞',
        icon: (
          <img src={reactionStatus.like ? likeSolid : likeHollow} alt="copy" />
        ),
        onClick: async () => {
          if (reactionStatus.dislike && !reactionStatus.like) {
            await IMSDK.reactMessage({
              conversationID,
              clientMsgID: message.clientMsgID,
              reaction: { emoji: '👎', action: 2 },
            });
          } else {
          }
          IMSDK.reactMessage({
            conversationID,
            clientMsgID: message.clientMsgID,
            reaction: { emoji: '👍', action: reactionStatus.like ? 2 : 1 },
          });
        },
      },
      {
        key: 'dislike',
        title: '点踩',
        icon: (
          <img
            src={reactionStatus.dislike ? dislikeSolid : dislikeHollow}
            alt="copy"
          />
        ),
        onClick: async () => {
          if (reactionStatus.like && !reactionStatus.dislike) {
            await IMSDK.reactMessage({
              conversationID,
              clientMsgID: message.clientMsgID,
              reaction: { emoji: '👍', action: 2 },
            });
          } else {
          }
          IMSDK.reactMessage({
            conversationID,
            clientMsgID: message.clientMsgID,
            reaction: { emoji: '👎', action: reactionStatus.dislike ? 2 : 1 },
          });
        },
      },
    ];
    if (!isInSmartAssitantantPage()) {
      return [
        // 只有当不是错误消息时才显示复制按钮
        ...(isError
          ? []
          : [
              {
                key: 'copy',
                title: '复制',
                icon: <img src={copyIcon} alt="copy" />,
                onClick: () => {
                  handleCopy(message, true);
                },
              },
            ]),

        // 只有最后一条消息才显示重新生成按钮
        ...(isLastMsg
          ? [
              {
                key: 'refresh',
                title: '重新生成',
                icon: <img src={refreshIcon} alt="refresh" />,
                onClick: handleRefresh,
              },
            ]
          : []),
      ];
    }
    // 否则根据是否是最后一条消息来决定显示哪些按钮
    else {
      return [
        // 只有当不是错误消息时才显示复制按钮
        ...(isError
          ? []
          : [
              {
                key: 'copy',
                title: '复制',
                icon: <img src={copyIconInSmart} alt="copy" />,
                onClick: () => {
                  handleCopy(message, true);
                },
              },
            ]),
        // 只有在智能助理里才显示朗读按钮
        ...[
          ...likeDislikeBtns,
          {
            key: 'read',
            title: '语音功能敬请期待',
            // currentId === message.clientMsgID && readingStatus !== 'idle'
            //   ? '停止朗读'
            //   : '朗读',
            icon:
              currentId === message.clientMsgID &&
              readingStatus === 'playing' ? (
                <WaveBars />
              ) : (
                <img src={readIconInSmart} alt="" />
              ),
            onClick: () => {
              // handleRead(message);
              feedbackToast({
                error: '语音功能敬请期待',
                msg: '语音功能敬请期待',
              });
            },
          },
        ],
        // 只有最后一条消息才显示重新生成按钮
        ...(isLastMsg
          ? [
              {
                key: 'refresh',
                title: '重新生成',
                icon: <img src={redoIconInSmart} alt="refresh" />,
                onClick: handleRefresh,
              },
            ]
          : []),
        // 只有在智能助理里才显示额外
        ...(!isInSmartAssitantantPage()
          ? []
          : [
              {
                key: 'delete',
                title: '删除',
                icon: <img src={deleteIconInSmart} alt="delete" />,
              },
            ]),
      ];
    }
  };

  const { content: messageContent, event } = content;

  const containerRef = useRef<HTMLDivElement>(null);
  const markInstanceRef = useRef<any>(null);

  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );
  const currentMultiSession = useConversationStore(
    (state) => state.currentMultiSession
  );
  const isMultiSession = useConversationStore((state) => state.isMultiSession);
  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );
  const { userDetail } = useUserInfo(currentConversation?.userID);
  const { sendStreamMessage } = useSendMessage(
    isMultiSession ? currentMultiSession : currentConversation
  );

  const [showExpandBtn, setShowExpandBtn] = useState(false);
  const [isExpand, setIsExpand] = useState(false);
  const setSelectObj = useSmartAssitantStore((state) => state.setSelectObj);

  // 使用钩子获取翻译数据，只有当相关数据变化时才会触发重新渲染
  const translationData = useTranslationStore((state) =>
    message.clientMsgID ? state.translations[message.clientMsgID] : undefined
  );

  const checkTextOverflow = () => {
    if (containerRef.current) {
      const isOverflow =
        containerRef.current.scrollHeight > containerRef.current.clientHeight;
      setShowExpandBtn(isOverflow);
    }
  };

  useEffect(() => {
    if (containerRef.current) {
      markInstanceRef.current = new Mark(containerRef.current);
    }
  }, []);

  useEffect(() => {
    checkTextOverflow();
  }, [messageContent]);

  useEffect(() => {
    if (markInstanceRef.current) {
      markInstanceRef.current.unmark();
      if (searchValue.trim() && isSearch) {
        markInstanceRef.current.mark(searchValue.trim(), {
          className: isInSmartAssitantantPage()
            ? styles.search_Highlight_isSmart
            : styles.search_Highlight,
        });

        const liItems =
          containerRef.current?.querySelectorAll('li.ordered-item');
        if (liItems) {
          highlightPseudoElement(liItems, searchValue.trim());
        }
      }
    }
  }, [isSearch, searchValue]);

  const handleRefresh = () => {
    const curConversation = isMultiSession
      ? currentMultiSession
      : currentConversation;
    const LinQAgentJump =
      localStorage.getItem(
        `smartAssistant.messageInput.questionType.${userID}`
      ) || '';

    console.debug('重新发送', {
      recvUser: userDetail,
      curMsg: message,
      lastMsg: prevMsg,
      curConversation,
      LinQAgentJump,
    });
    sendStreamMessage({
      recvUser: userDetail,
      curMsg: message,
      lastMsg: prevMsg,
      curConversation,
      LinQAgentJump,
    });
  };

  const handleDeleteMsg = () => {};

  if (messageContent?.error) {
    const errorContent =
      messageContent.error.code === '999'
        ? '抱歉，系统开小差了，请稍后重试'
        : messageContent.error.msg;
    return (
      <div className={styles.streamMessage}>
        <div className={styles.errorContent}>{errorContent}</div>
        {!inHistoryList && (
          <div className={styles.errorFooter}>
            <div className={styles.actionButtonWrapper}>
              <ActionButtons
                message={message}
                onRefresh={handleRefresh}
                buttons={getActionButtons({
                  messageContent,
                  message,
                  conversationID,
                  isLastMsg,
                  handleRefresh,
                  isError: true,
                })}
                conversationID={
                  isMultiSession
                    ? currentMultiSession?.subConversationId
                    : currentConversation?.conversationID
                }
              />
            </div>
            <span className={styles.errorStatus}>运行失败</span>
          </div>
        )}
      </div>
    );
  }

  const renderSmartHistoryContent = () => {
    return (
      <Space direction="vertical" size={12} className={styles.messageContainer}>
        <div>
          {messageContent.answer === AILoadingText ||
          messageContent?.answer == null ||
          messageContent?.answer === '' ? (
            <Loading />
          ) : (
            <>
              <div
                className={classNames(
                  styles.fadeInContent,
                  styles.isSmartHistory,
                  isExpand && styles.bubble_isExpand
                )}
                ref={containerRef}
              >
                <RenderMd
                  id={message.clientMsgID}
                  value={processImageUrls(messageContent.answer)}
                  hasKnowledgeRetrieve={
                    !!messageContent?.knowledge_retrieve?.results
                  }
                  hasNetworkSearch={!!messageContent?.network_search?.results}
                  referenceClick={(val: number, type: 'know' | 'network') => {
                    const idx = val - 1 >= 0 ? val - 1 : 0;
                    const data =
                      type === 'know'
                        ? messageContent?.knowledge_retrieve?.results
                        : messageContent?.network_search?.results;
                    const obj = data?.[idx] ? data[idx] : undefined;
                    if (!isInSmartAssitantantPage()) {
                      changeRightArea(
                        type === 'know'
                          ? 'OPEN_ROBOT_ANSWER_SOURCE'
                          : 'OPEN_ROBOT_ONLINE_SEARCE',
                        {
                          data,
                          activeObj: obj,
                        }
                      );
                    } else {
                      setSelectObj(obj);
                    }
                  }}
                />
              </div>
              {showExpandBtn && (
                <div
                  className={classNames(
                    styles.toggleBtn,
                    isExpand && styles.isExpand
                  )}
                  onClick={() => {
                    setIsExpand(!isExpand);
                  }}
                >
                  {isExpand ? '收起' : '展开'}
                  <img src={expandIcon} />
                </div>
              )}
            </>
          )}
        </div>
      </Space>
    );
  };
  if (isInSmartAssitantantPage() && inHistoryList) {
    return renderSmartHistoryContent();
  }
  return (
    <Space
      direction="vertical"
      size={isInSmartAssitantantPage() ? 12 : 8}
      className={styles.messageContainer}
    >
      {isInSmartAssitantantPage() &&
        (messageContent?.think?.answer ||
          messageContent?.plugins?.length > 0) && (
          <ThinkContent
            content={messageContent}
            clientMsgID={message.clientMsgID}
            isLastMsg={isLastMsg}
          />
        )}

      <div ref={containerRef}>
        {messageContent.answer === AILoadingText ||
        messageContent?.answer == null ||
        messageContent?.answer === '' ? (
          <Loading />
        ) : (
          <div className={styles.fadeInContent}>
            <RenderMd
              id={message.clientMsgID}
              value={processImageUrls(messageContent.answer)}
              hasKnowledgeRetrieve={
                !!messageContent?.knowledge_retrieve?.results
              }
              hasNetworkSearch={!!messageContent?.network_search?.results}
              referenceClick={(val: number, type: 'know' | 'network') => {
                const idx = val - 1 >= 0 ? val - 1 : 0;
                const data =
                  type === 'know'
                    ? messageContent?.knowledge_retrieve?.results
                    : messageContent?.network_search?.results;
                const obj = data?.[idx] ? data[idx] : undefined;
                if (!isInSmartAssitantantPage()) {
                  changeRightArea(
                    type === 'know'
                      ? 'OPEN_ROBOT_ANSWER_SOURCE'
                      : 'OPEN_ROBOT_ONLINE_SEARCE',
                    {
                      data,
                      activeObj: obj,
                    }
                  );
                } else {
                  setSelectObj(obj);
                }
              }}
            />
          </div>
        )}
      </div>

      {!isSearch && !isForwardMessage && !inMultiMessageModal && (
        <>
          {/* 添加翻译组件 */}
          {translationData && (
            <TranslationRender
              translationData={translationData}
              messageId={message.clientMsgID}
            />
          )}

          {messageContent?.knowledge_retrieve?.results &&
            (isInSmartAssitantantPage() ? !!messageContent?.end : true) && (
              <KnowledgeContent
                content={messageContent.knowledge_retrieve}
                fromMsgID={message.clientMsgID}
              />
            )}

          {messageContent?.network_search?.results &&
            (isInSmartAssitantantPage() ? !!messageContent?.end : true) && (
              <NetworkContent
                content={{
                  results: messageContent.network_search.results,
                }}
                fromMsgID={message.clientMsgID}
              />
            )}

          {!!messageContent?.end && (
            <div className={styles.actionFooter}>
              <ActionButtons
                message={message}
                onRefresh={handleRefresh}
                onFeedback={(type) => {
                  // TODO: 点赞点踩逻辑，需根据实际业务实现
                }}
                buttons={getActionButtons({
                  messageContent,
                  message,
                  conversationID,
                  isLastMsg,
                  handleRefresh,
                })}
                conversationID={
                  isMultiSession
                    ? currentMultiSession?.subConversationId
                    : currentConversation?.conversationID
                }
              />
              {!isInSmartAssitantantPage() && (
                <Cost
                  content={{
                    latency: messageContent.latency,
                    input_tokens: messageContent.input_tokens,
                    output_tokens: messageContent.output_tokens,
                  }}
                />
              )}
            </div>
          )}

          {/* {message.clientMsgID === clientMsgId &&
            questions &&
            !!messageContent?.end && (
              <GuideQuestions
                questions={questions}
                currentConversation={currentConversation}
              />
            )} */}
        </>
      )}
    </Space>
  );
};

export default StreamMessageRender;
