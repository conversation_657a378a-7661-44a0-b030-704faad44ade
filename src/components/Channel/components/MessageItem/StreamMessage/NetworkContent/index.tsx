import { FC } from 'react';
import networkIcon from '@/assets/channel/messageRender/network.png';
import { useConversationStore, useSmartAssitantStore } from '@/store';
import { isInSmartAssitantantPage } from '@/utils/utils';
import KnowledgeButton from '../KnowledgeContent/KnowledgeButton';

interface NetworkItem {
  index: number;
  title: string;
  url: string;
  content: string;
}

interface NetworkContentProps {
  content: {
    text?: string;
    url?: string;
    results?: NetworkItem[];
    query?: string;
    latency?: number;
  };
  fromMsgID?: string;
}

const NetworkContent: FC<NetworkContentProps> = ({ content, fromMsgID }) => {
  const { results = [] } = content;

  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );

  const rightAreaInSmart = useSmartAssitantStore(
    (state) => state.rightAreaInSmart
  );
  const fromMsgIDInSmart = useSmartAssitantStore((state) => state.fromMsgID);
  const changeRightAreaInSmart = useSmartAssitantStore(
    (state) => state.changeRightArea
  );
  const clearRightAreaInSmart = useSmartAssitantStore(
    (state) => state.clearRightArea
  );

  if (!results || results.length === 0) {
    return null;
  }

  return (
    <div>
      <KnowledgeButton
        icon={<img src={networkIcon} alt="" />}
        text={`${results.length}个网络结果`}
        onClick={() => {
          if (!isInSmartAssitantantPage()) {
            changeRightArea('OPEN_ROBOT_ONLINE_SEARCE', {
              data: results,
            });
          } else if (
            rightAreaInSmart === 'onlineSearch' &&
            fromMsgIDInSmart === fromMsgID
          ) {
            clearRightAreaInSmart();
          } else {
            changeRightAreaInSmart('onlineSearch', {
              data: results,
              fromMsgID,
            });
          }
        }}
      />
    </div>
  );
};

export default NetworkContent;
