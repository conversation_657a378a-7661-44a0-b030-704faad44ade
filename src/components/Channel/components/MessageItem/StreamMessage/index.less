.messageContainer {
  display: flex;

}
.fadeInContent {
  &.isSmartHistory {
    word-break: break-all;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 控制显示的行数 */
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &.bubble_isExpand {
    -webkit-line-clamp: unset;
  }
}
.streamMessage {
  .errorContent {
    overflow-wrap: anywhere;
  }
  .errorFooter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;

    .actionButtonWrapper {
      display: flex;
      align-items: center;
      flex: 1;
      img {
        width: 28px;
        height: 28px;
      }
    }

    .errorStatus {
      font-size: 13px;
      color: var(--primary-text-color-3);
      display: flex;
      align-items: center;
      height: 20px;
    }
  }
}

/* 图标样式 */
.refreshIcon {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

/* 操作区域样式 */
.actionFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  img {
    width: 28px;
    height: 28px;
  }
}

.search_Highlight {
  padding: 0 !important;
  background: #fff5da !important;
  border-radius: 2px;
}

.search_Highlight_isSmart {
  background: transparent !important;
  color: #2962ff;
  padding: 0 !important;
}

.toggleBtn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 14px;
  color: #007efc;
  line-height: 22px;
  cursor: pointer;

  > img {
    margin-left: 5px;
  }

  &.isExpand {
    > img {
      transform: rotate(180deg);
    }
  }
}
