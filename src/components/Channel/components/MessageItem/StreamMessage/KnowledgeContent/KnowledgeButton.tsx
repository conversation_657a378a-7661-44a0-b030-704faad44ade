import React, { FC, ReactNode } from 'react';
import { isInSmartAssitantantPage } from '@/utils/utils';
import classNames from 'classnames';
import styles from './index.less';

interface KnowledgeButtonProps {
  icon?: ReactNode;
  text: string;
  onClick?: () => void;
  style?: React.CSSProperties;
}

const KnowledgeButton: FC<KnowledgeButtonProps> = ({
  icon,
  text,
  onClick,
  style,
}) => {
  return (
    <div
      className={classNames(
        styles.container,
        isInSmartAssitantantPage() ? styles.inSmart : ''
      )}
      onClick={onClick}
      style={style}
    >
      {!isInSmartAssitantantPage() ? icon : null}
      <span className={styles.text}>{text}</span>
      {isInSmartAssitantantPage() ? icon : null}
    </div>
  );
};

export default KnowledgeButton;
