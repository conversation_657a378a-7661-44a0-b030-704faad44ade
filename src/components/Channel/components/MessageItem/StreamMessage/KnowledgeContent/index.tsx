import { FC } from 'react';
import indexIcon from '@/assets/smartAssistant/indexIcon.svg';
import knowledgeIcon from '@/assets/channel/messageRender/knowledge.png';
import { useConversationStore, useSmartAssitantStore } from '@/store';
import { isInSmartAssitantantPage } from '@/utils/utils';
import KnowledgeButton from './KnowledgeButton';

interface KnowledgeItem {
  index: number;
  score: number;
  doc_name: string;
  doc_type: number;
  doc_url?: string;
  content: string;
}

interface KnowledgeContentProps {
  content: {
    results: KnowledgeItem[];
    query?: string;
    latency?: number;
  };
  fromMsgID?: string;
}

const KnowledgeContent: FC<KnowledgeContentProps> = ({
  content,
  fromMsgID,
}) => {
  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );
  const rightAreaInSmart = useSmartAssitantStore(
    (state) => state.rightAreaInSmart
  );
  const fromMsgIDInSmart = useSmartAssitantStore((state) => state.fromMsgID);
  const changeRightAreaInSmart = useSmartAssitantStore(
    (state) => state.changeRightArea
  );
  const clearRightAreaInSmart = useSmartAssitantStore(
    (state) => state.clearRightArea
  );

  const { results = [], query } = content;

  if (!results || results.length === 0) {
    return null;
  }

  return (
    <div>
      <KnowledgeButton
        icon={
          <img
            src={isInSmartAssitantantPage() ? indexIcon : knowledgeIcon}
            alt=""
          />
        }
        text={`${results.length}个回答来源`}
        onClick={() => {
          if (!isInSmartAssitantantPage()) {
            changeRightArea('OPEN_ROBOT_ANSWER_SOURCE', {
              data: results,
            });
          } else if (
            rightAreaInSmart === 'resource' &&
            fromMsgIDInSmart === fromMsgID
          ) {
            clearRightAreaInSmart();
          } else {
            changeRightAreaInSmart('resource', { data: results, fromMsgID });
          }
        }}
      />
    </div>
  );
};

export default KnowledgeContent;
