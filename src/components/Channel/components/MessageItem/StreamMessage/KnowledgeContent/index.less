.container {
  background-color: color-mix(
    in srgb,
    var(--tab-actived-background-color),
    transparent 70%
  );

  &.inSmart {
    border-radius: 14px;
    border: 1px solid #dddee0;
    background-color: #fff;

    > img {
      margin-left: 4px;
    }
  }

  display: inline-flex;
  align-items: center;
  padding: 5px 12px;
  border-radius: 14px;
  cursor: pointer;
  us
  .text {
    margin-left: 2px;
    font-size: 13px;
    color: var(--primary-text-color-1);
  }
}
