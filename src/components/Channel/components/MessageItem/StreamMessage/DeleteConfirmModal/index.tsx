import { Modal } from '@ht/sprite-ui';
import warningIcon from '@/assets/smartAssistant/warningIcon.svg';
import closeModalIcon from '@/assets/smartAssistant/closeModalIcon.svg';
import styles from './index.less';

interface DeleteConfirmModalProps {
  isOpen: boolean;
  handleCancel: () => void;
  onDelete: () => void;
}

const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({
  isOpen,
  handleCancel,
  onDelete,
}) => {
  return (
    <Modal
      onCancel={() => handleCancel()} // 取消按钮回调
      footer={null} // 隐藏默认的底部按钮
      width={480}
      open={isOpen}
      centered={true}
      closable={false}
      className={styles.deleteModal}
      maskClosable={false}
    >
      <div className={styles.modalContent}>
        <div className={styles.header}>
          <img className={styles.icon} src={warningIcon}></img>
          <div className={styles.title}>确认删除</div>
          <div className={styles.deleteIcon}>
            <img src={closeModalIcon} onClick={handleCancel}></img>
          </div>
        </div>
        <div className={styles.content}>
          删除后，聊天记录不可恢复，确认要删除吗
        </div>
        <div className={styles.footer}>
          <div className={styles.cancelBtn} onClick={handleCancel}>
            取消
          </div>
          <div className={styles.confirmBtn} onClick={onDelete}>
            确认删除
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteConfirmModal;
