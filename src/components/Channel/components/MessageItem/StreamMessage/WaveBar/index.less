.bar {
    transform-origin: bottom center;
    animation: wave 1.6s infinite;
    animation-timing-function: ease-in-out;
}

/* 动画错开延迟，产生自然波动 */
.bar:nth-child(1) {
    animation-delay: 0s;
}
.bar:nth-child(2) {
    animation-delay: 0.3s;
}
.bar:nth-child(3) {
    animation-delay: 0.6s;
}
.bar:nth-child(4) {
    animation-delay: 0.9s;
}

@keyframes wave {
    0%,
    100% {
        transform: scaleY(1);
    } /* 动画开始时不干扰初始高度 */
    25% {
        transform: scaleY(0.7);
    }
    50% {
        transform: scaleY(1);
    }
    75% {
        transform: scaleY(0.8);
    }
}
