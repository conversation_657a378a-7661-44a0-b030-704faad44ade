import styles from './index.less';

const WaveBars = ({ width = 16, height = 16 }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 16 16"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g stroke="#2962FF" strokeLinecap="round" strokeWidth="1.5">
        {/* 初始高度不同 */}
        <line className={styles.bar} x1="1" y1="4" x2="1" y2="6" />
        <line className={styles.bar} x1="5" y1="2" x2="5" y2="8" />
        <line className={styles.bar} x1="9" y1="1" x2="9" y2="9" />
        <line className={styles.bar} x1="13" y1="4" x2="13" y2="6" />
      </g>
    </svg>
  );
};

export default WaveBars;
