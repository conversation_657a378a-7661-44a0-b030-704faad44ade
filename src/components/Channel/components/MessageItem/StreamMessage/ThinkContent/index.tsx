import { FC, useEffect, useState } from 'react';
import arrowIcon from '@/assets/stream/arrow.svg';
import { Steps } from '@ht/sprite-ui';
import type { StepsProps } from '@ht/sprite-ui';
import { LoadingOutlined } from '@ht-icons/sprite-ui-react';
import check from '@/assets/stream/check.svg';
import classNames from 'classnames';
import { isInSmartAssitantantPage } from '@/utils/utils';
import styles from './index.less';

const { Step } = Steps;

interface ThinkContentProps {
  content: {
    think?: {
      answer: string;
    };
    plugins: PluginCallProps[];
  };
  clientMsgID: string;
  isLastMsg: boolean;
}

interface PluginCallProps {
  clientMsgId: string;
  conversation_id: string;
  created_at: number;
  event: string;
  index: number;
  latency?: number;
  name: string;
  operation_id: string;
  status?: number;
  trace_id: string;
  start?: number;
  end?: number;
  answer?: string;
}

const customDot: StepsProps['progressDot'] = (dot, { status, index }) => {
  if (status === 'finish') {
    return (
      <img
        style={{
          width: '12px',
          height: '12px',
          position: 'relative',
          top: '1.5px',
        }}
        src={check}
      />
    );
  } else if (status === 'wait') {
    return (
      <LoadingOutlined width={12} height={12} className={styles.loading} />
    );
  } else {
    return <></>;
  }
};

const ThinkContent: FC<ThinkContentProps> = ({
  content,
  clientMsgID,
  isLastMsg,
}) => {
  const thinkText = content?.think?.answer;
  const plugins = content?.plugins;
  const [isExpanded, setIsExpanded] = useState(false);
  useEffect(() => {
    if (!isLastMsg) {
      setIsExpanded(false);
    } else {
      setIsExpanded(true);
    }
  }, [clientMsgID, isLastMsg]);

  if (!thinkText && !plugins?.length) {
    return <></>;
  }

  return (
    <div className={styles.thinkContainer}>
      <div
        className={styles.thinkTitle}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        思考规划
        <span
          className={
            isExpanded
              ? styles.thinkArrow
              : `${styles.thinkArrow} ${styles.collapsed}`
          }
        >
          <img src={arrowIcon} alt="arrow" />
        </span>
      </div>
      <div
        className={classNames(
          `${styles.thinkContentContainer} ${
            isExpanded ? styles.expanded : styles.collapsed
          }`,
          isInSmartAssitantantPage() && styles.inSmart
        )}
      >
        {content?.plugins && (
          <Steps
            progressDot={customDot}
            direction="vertical"
            current={content?.plugins?.length + 1}
          >
            {content.plugins.map((data: PluginCallProps, index: number) => {
              let time = 0;
              const title =
                data.name + (data.latency ? ` - ${data.latency}s` : '');
              let description = <></>;
              let status = 'wait';
              if (data.clientMsgId) {
                // 是过程渲染状态
                time = data.created_at * 1000;
                // description = data.event;
                status = data?.event?.endsWith('_end') ? 'finish' : 'wait';
              } else {
                // 是结果渲染状态

                time = (data.end || 0) * 1000;
                // description = data.answer;
                status = data.status === 0 ? 'finish' : 'wait';
              }
              if (data.answer) {
                description = (
                  <div className={styles.stepDesc}>{data.answer}</div>
                );
              }
              return (
                <Step
                  title={`${new Date(time).toLocaleString()} - ${title}`}
                  key={time}
                  description={description}
                  status={status}
                />
              );
            })}
          </Steps>
        )}
        {thinkText}
      </div>
    </div>
  );
};

export default ThinkContent;
