.thinkContainer {
  // margin-bottom: 12px;
}

.thinkTitle {
  display: flex;
  align-items: center;
  user-select: none;
  width: 102px;
  height: 20px;
  font-size: 12px;
  font-weight: 600;
  color: #999ba0;
  line-height: 20px;
  cursor: pointer;
}

.thinkArrow {
  margin-left: 4px;
  transition: transform 0.3s ease;
  display: inline-block;
  width: 12px;
  height: 12px;

  img {
    width: 100%;
    height: 100%;
    display: block;
    transition: transform 0.3s ease;
    // transform: rotate(-90deg);
  }

  &.collapsed img {
    transform: rotate(180deg);
  }
}

.thinkContentContainer {
  padding-top: 8px;
  line-height: 24px;
  border-radius: 8px;
  font-size: 13px;
  color: #74757e;
  transition: all 0.3s ease;

  &.collapsed {
    display: none; //直接不显示，否则会影响actionBtns的hover
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
    margin-bottom: 0;
    opacity: 0;
  }

  &.expanded {
    max-height: 1000px;
    overflow: auto;
    opacity: 1;

    &.insmart {
      max-height: none;
    }
  }

  :global {
    .linkflow-steps-item-content {
      min-height: 28px;
      .linkflow-steps-item-title {
        font-size: 14px;
        color: #999ba0 !important;
        line-height: 24px;
        margin-bottom: 4px;
      }

      .linkflow-steps-item-description {
        padding-bottom: 0;
      }

      min-height: 24px !important;
    }

    // .linkflow-steps-item {
    //   &:not(:nth-child(1)) {
    //     margin-top: 4px !important;
    //   }
    // }

    .linkflow-steps-item-icon {
      margin-top: 0 !important;
      margin-right: 10px !important;
    }

    .linkflow-steps-item-tail {
      top: 8.5px !important;
      left: -7px !important;
      margin: 0 !important;
      padding: 10px 0 4px !important;

      &::after {
        background-color: #dddee0 !important;
      }
    }
  }

  .loading {
    top: 1px;
    width: 12px;
    height: 12px;
    position: relative;
    color: #999ba0;
    svg {
      width: 12px;
      height: 12px;
    }
  }

  .stepDesc {
    padding-left: 10px;
    font-size: 14px;
    color: #999ba0;
    line-height: 24px;
    border-left: 2px solid #f2f2f3;
    margin-bottom: 4px;
  }
}
