/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
/* eslint-disable react/no-danger */
/* eslint-disable indent */
import { FC, memo, useRef, useState, useEffect } from 'react';
import classNames from 'classnames';
import Mark from 'mark.js';
import useUserInfo from '@/hooks/useUserInfo';
import _ from 'lodash';
import { useTranslationStore } from '@/store/translationStore';
import { useConversationStore } from '@/store';
import { getUserRoles, userRolesType } from '@/utils/avatar';
import { RenderMd } from '@/components/MdEditor/RenderMd';
import { shallow } from 'zustand/shallow';
import { AILoadingText } from '@/utils/constants';
import { isInSmartAssitantantPage } from '@/utils/utils';
import expandIcon from '@/assets/smartAssistant/expandIcon.svg';
import styles from './index.less';
import UserInfoRender from './UserInfoRender';
import RobotCommandRender from './RobotCommandRender';
import { startsWithSlashNoSpace } from '../MessageInput';
import TranslationRender from './TranslationRender';
import { IMessageItemProps } from '.';

// 高亮有序列表的序号
export const highlightPseudoElement = (items: any, value: string) => {
  const isSmart = isInSmartAssitantantPage();

  items.forEach((item: any) => {
    const original = item.dataset.content;
    if (!original) {
      return;
    }

    const highlighted = original.replace(
      new RegExp(value.trim(), 'g'),
      `<span class='${
        isSmart
          ? `${styles.search_Highlight_isSmart}`
          : `${styles.search_Highlight}`
      }' style="font-size: 16px">$&</span>`
    );

    const wrapper = document.createElement('div');
    wrapper.innerHTML = `${highlighted}`;
    wrapper.style =
      'position: absolute;left: 4px;top: 0;display: flex;align-items: center;justify-content: center;width: auto;height: 22px;';

    if (item.firstChild) {
      item.insertBefore(wrapper, item.firstChild);
    } else {
      item.appendChild(wrapper);
    }
    item.dataset.content = '';
  });
};
interface TextMessageRenderProps extends IMessageItemProps {
  isSourceForQuote?: boolean;
}

const TextMessageRender: FC<TextMessageRenderProps> = ({ ...props }) => {
  const {
    message,
    isSender,
    scrollToBottomSmooth,
    hasScrolled,
    isSourceForQuote = false,
    isSearch = false,
    searchValue = '',
    inMultiMessageModal,
    inHistoryList,
  } = props;
  const { currentConversation, llmLoading } = useConversationStore(
    (state) => ({
      currentConversation: state.currentConversation,
      llmLoading: state.llmLoading,
    }),
    shallow
  );
  const userRoles = getUserRoles(message.sendID);
  const { userDetail } = useUserInfo(currentConversation?.userID);

  // 只获取当前消息的翻译数据，提高性能
  const translationData = useTranslationStore((state) =>
    message.clientMsgID ? state.translations[message.clientMsgID] : undefined
  );
  const [showRefresh, setShowRefresh] = useState(false);
  const [isStreaming, setIsStreaming] = useState<boolean>(false);
  const content =
    message.textElem?.content ||
    message.quoteElem?.text ||
    message.atTextElem?.text ||
    '';

  const markInstanceRef = useRef<any>(null);
  const [showExpandBtn, setShowExpandBtn] = useState(false);
  const [isExpand, setIsExpand] = useState(false);

  useEffect(() => {
    if (llmLoading && hasScrolled && isStreaming) {
      scrollToBottomSmooth?.();
    } else if (
      llmLoading &&
      isStreaming &&
      message.textElem?.content === AILoadingText
    ) {
      scrollToBottomSmooth?.();
    }
  }, [hasScrolled, showRefresh, llmLoading, message]);

  useEffect(() => {
    async function fetchStreamInfo() {
      if (!currentConversation?.latestMsg || !userDetail || isSender) {
        return;
      }
      const latestMsgObj = JSON.parse(currentConversation.latestMsg);
      const { seq } = latestMsgObj;
      if (userDetail?.ex) {
        try {
          const exInfo = JSON.parse(userDetail.ex);
          const { stream = false } = exInfo;
          // 只有当该消息的 seq 和最新消息的 seq 相等时才显示刷新按钮
          setShowRefresh(stream && seq === message.seq);
          setIsStreaming(stream && seq === message.seq);
        } catch (e) {
          console.warn('解析 ex 失败:', e);
        }
      }
    }
    fetchStreamInfo();
  }, [currentConversation?.latestMsg, message.seq, isSender, userDetail]);

  const [showExpand, setShowExpand] = useState(false);
  const [expanded, setExpanded] = useState(true);
  // const [mdHeight, setMdHeight] = useState(30);
  const containerRef = useRef<HTMLDivElement>(null);
  // translationData已经通过钩子直接获取

  // const getMdSize = (size: any) => {
  //   if (!size) {
  //     return;
  //   }
  //   const { height } = size;
  //   // setMdHeight(height);
  //   // setTooltipHeight(height);
  //   //
  //   if (Number(height) > 600) {
  //     setShowExpand(true);
  //   } else {
  //     setShowExpand(false);
  //   }
  // };

  // useUpdateLayoutEffect(() => {
  //   if (mdHeight > 0 && containerRef.current) {
  //     containerRef.current.style.height = `${mdHeight}px`;
  //   }
  // }, [mdHeight]);

  const checkTextOverflow = () => {
    if (containerRef.current) {
      const isOverflow =
        containerRef.current.scrollHeight > containerRef.current.clientHeight;
      setShowExpandBtn(isOverflow);
    }
  };
  useEffect(() => {
    if (containerRef.current) {
      markInstanceRef.current = new Mark(containerRef.current);
    }
    checkTextOverflow();
  }, []);

  useEffect(() => {
    if (markInstanceRef.current) {
      markInstanceRef.current.unmark();
      if (searchValue.trim() && isSearch) {
        markInstanceRef.current.mark(searchValue.trim(), {
          className: isInSmartAssitantantPage()
            ? styles.search_Highlight_isSmart
            : styles.search_Highlight,
        });
        const liItems =
          containerRef.current?.querySelectorAll('li.ordered-item');
        if (liItems) {
          highlightPseudoElement(liItems, searchValue.trim());
        }
      }
    }
  }, [isSearch, searchValue]);

  const renderSmartHistoryContent = () => {
    return (
      <div>
        <div
          ref={containerRef}
          style={{
            width: '100%',
            overflow: 'hidden',
            position: 'relative',
          }}
          className={classNames(
            styles.bubble,
            isSender ? styles.isSender : styles.isReceiver,
            styles.isSmartHistory,
            isExpand && styles.bubble_isExpand
          )}
        >
          <RenderMd
            // getSize={getMdSize}
            id={message.clientMsgID}
            value={content}
            isSender={isSender}
          />
        </div>
        {showExpandBtn && (
          <div
            className={classNames(
              styles.toggleBtn,
              isExpand && styles.isExpand
            )}
            onClick={() => {
              setIsExpand(!isExpand);
            }}
          >
            {isExpand ? '收起' : '展开'}
            <img src={expandIcon} />
          </div>
        )}
      </div>
    );
  };

  const renderContent = () => {
    if (
      startsWithSlashNoSpace(content) &&
      (userRoles === userRolesType.developers ||
        userRoles === userRolesType.employees)
    ) {
      return <RobotCommandRender content={content} />;
    } else if (isInSmartAssitantantPage() && inHistoryList) {
      return renderSmartHistoryContent();
    } else {
      return (
        <div>
          <div
            ref={containerRef}
            style={{
              // maxHeight: !expanded ? '600px' : '100%',
              // maxHeight:
              //   (isStreaming && llmLoading) || expanded ? '100%' : '600px',
              width: '100%',
              overflow: 'hidden',
              position: 'relative',
            }}
            className={classNames(
              styles.bubble,
              isSender ? styles.isSender : styles.isReceiver
            )}
          >
            <RenderMd
              // getSize={getMdSize}
              id={message.clientMsgID}
              value={content}
              isSender={isSender}
            />

            {/* {showExpand && !expanded && !(isStreaming && llmLoading) && (
              <div
                style={{
                  position: 'absolute',
                  bottom: 0,
                  height: '52px',
                  width: '100%',
                  background: `linear-gradient(transparent, ${
                    isSender
                      ? 'var(--primary-background-color-12)'
                      : 'var(--primary-background-color-6)'
                  })`,
                }}
              ></div>
            )} */}
          </div>
          {translationData && !isSearch && !inMultiMessageModal && (
            <TranslationRender
              translationData={translationData}
              messageId={message.clientMsgID}
            />
          )}
        </div>
      );
    }
  };

  return isSourceForQuote ? (
    renderContent()
  ) : (
    <UserInfoRender {...props}>{renderContent()}</UserInfoRender>
  );
};

export default memo(TextMessageRender);
