@import "src/pages/smartAssistantHalfPage/breakpoint.less";
.messageWarp {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;

  @inputBoxHeight: 164px;

  .messageUpWrap {
    position: absolute;
    padding-top: 4px;
    top: 0;
    width: calc(100% - 8px);
    left: 4px;
    z-index: 1;
    background-color: var(--primary-background-color-8);
  }

  .messageContent {
    flex: 1;
    // height: calc(100% - @inputBoxHeight);
    overflow-y: auto;

    &.inHalfSmart {
      margin: 0;

      @media (max-height: @half-collapse-breakpoint2) {
        margin: 0;
      }
      @media (max-height: @half-collapse-breakpoint) {
        display: none;
      }
    }

    .box {
      height: 100%;
      position: relative;
      // padding-left: 13px;
      .loadingBox {
        height: 100%;
        // display: flex;
        // justify-content: center;
        // align-items: center;
      }
      .virtuosoListContainer {
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        transform: translateZ(0);
        will-change: opacity;

        > div > div > div {
          &:last-of-type {
            .lastMultiSessionItem {
              height: 22px;
            }
          }
        }

        &::-webkit-scrollbar {
          width: 8px;
          background-color: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background-color: transparent;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-track {
          background-color: transparent;
        }

        &:hover::-webkit-scrollbar {
          width: 8px;
        }

        &:hover::-webkit-scrollbar-thumb {
          background-color: rgba(0, 0, 0, 20%);
          border-radius: 3px;
        }

        &:hover::-webkit-scrollbar-track {
          background-color: transparent;
        }
      }

      .virtuosoListContainerSmart {
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        transform: translateZ(0);
        will-change: opacity;

        > div > div > div {
          &:last-of-type {
            .lastMultiSessionItem {
              height: 22px;
            }
          }
        }

        &::-webkit-scrollbar {
          display: none;
        }

        &::-webkit-scrollbar-thumb {
          display: none;
        }

        &::-webkit-scrollbar-track {
          display: none;
        }

        &:hover::-webkit-scrollbar {
          display: none;
        }

        &:hover::-webkit-scrollbar-thumb {
          display: none;
        }

        &:hover::-webkit-scrollbar-track {
          display: none;
        }
      }

      .unReadCount {
        position: absolute;
        right: 38px;
        bottom: 12px;
        width: 110px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--primary-background-color-6);
        box-shadow: 0 2px 14px 2px var(--primary-background-color-13);
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        color: var(--primary-text-color-9);
        cursor: pointer;
        > img {
          width: 14px;
          height: 14px;
          margin-left: 8px;
        }
      }

      .unReadInSmart {
        width: 36px;
        height: 36px;
        background: #ffffff;
        border: 1px solid #d7d7d7;
        position: absolute;
        left: calc(50% - 18px);
        bottom: 0;
        z-index: 10;
        border-radius: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: opacity 0.3s ease; /* 渐隐动画 */
        > img {
          width: 14px;
          height: 7px;
        }
      }

      .gotoFirstUnReadBtn {
        cursor: pointer;
        position: absolute;
        right: 20px;
        top: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 129px;
        height: 28px;
        background: #ffffff;
        box-shadow: 0 2px 14px 2px rgba(0, 0, 0, 10%);
        border-radius: 20px;

        span {
          font-size: 14px;
          font-weight: 600;
          color: #0074e2;
          line-height: 20px;
        }
        img {
          margin-left: 8px;
        }
      }
    }
  }

  .netErrorArea {
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    font-weight: 600;
    color: var(--primary-text-color-1);
    height: 100%;

    img {
      width: 28px;
      margin-right: 6px;
    }
  }

  .messageInputWrap {
    // min-height: @inputBoxHeight;
    padding: 24px 20px 20px;
    display: flex;
    flex-direction: column;
    position: relative;

    .typingStatus {
      flex-shrink: 0;
      font-size: 14px;
      font-weight: 400;
      color: var(--primary-text-color);
      line-height: 20px;
      height: 20px;
      position: absolute;
      top: 0;
      left: 22px;
    }
    @keyframes dot-sequence {
      0%,
      20% {
        content: ".";
      }
      40% {
        content: ". .";
      }
      60% {
        content: ". . .";
      }
      80%,
      100% {
        content: "";
      }
    }
    .dot {
      margin-left: 5px;
      &::after {
        width: 30px;
        content: ". . .";
        position: absolute;
        animation: dot-sequence 2s infinite;
      }
    }
  }
  .exitBox {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 66px;
    background: var(--primary-background-color-15);
    font-size: 16px;
    font-weight: 400;
    color: var(--primary-text-color-8);
    border-radius: 0 0 8px 8px;
    > img {
      width: 16px;
      margin-right: 6px;
    }
  }
}

.multiSelectBottomWrapper {
  width: 100%;
  height: 128px;
  background: #f6f6f7;
  border-radius: 8px 8px 0 0;
  padding-top: 24px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #dddee0;

  .btnGroup {
    display: flex;

    .btnItemWrapper {
      margin-right: 80px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .btnLabel {
        margin-top: 12px;
        font-size: 14px;
        color: #2f3035;
        line-height: 22px;
      }

      .btnIconWrapper {
        .btnIcon {
          display: block;
        }

        .btnHoverIcon {
          display: none;
        }

        &:hover {
          cursor: pointer;
          .btnIcon {
            display: none;
          }

          .btnHoverIcon {
            display: block;
          }
        }
      }

      .btnIconWrapper_disabled {
        opacity: 0.5;
        &:hover {
          cursor: default;
          .btnIcon {
            display: block;
          }

          .btnHoverIcon {
            display: none;
          }
        }
      }
    }
  }

  .cancelBtn {
    margin-top: 9px;
    width: 28px;
    height: 28px;

    &:hover {
      cursor: pointer;
      background: rgba(107, 107, 108, 8%);
      border-radius: 6px;
    }
  }
}
