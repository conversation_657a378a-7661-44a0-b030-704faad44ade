/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable max-statements */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable max-lines */
import {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { IMSDK } from '@/layouts/BasicLayout';
import { START_INDEX, useHistoryMessageList } from '@/hooks/useHistoryMessageList';
import { useConversationStore, useUserStore, useMultiSelectStore, useSmartAssitantStore } from '@/store';
import {
  MessageItem as MessageItemType,
  ConversationItem,
  GroupStatus,
  GroupMemberRole,
} from '@ht/openim-wasm-client-sdk';
import {
  LogLevel,
  Virtuoso,
  VirtuosoHandle,
} from '@ht/react-virtuoso';
import useConversationState from '@/hooks/useConversationState';
import emitter, { emit } from '@/utils/events';
import _, { throttle, isEmpty, head } from 'lodash';
import { useDeepCompareEffect, useLatest } from 'ahooks';
import offlineIcon from '@/assets/images/offlineIcon.png';
import exitIcon from '@/assets/images/exit.svg';
import disableContactIcon from '@/assets/images/disableContact.svg';
import doubleDownArrowIcon from '@/assets/images/doubleDownArrow.svg';
import { useMessageScroll } from '@/hooks/useMessageScroll';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { useConversationSettings } from '@/hooks/useConversationSettings';
import {  useResizeObserverWithThreshold } from '@/hooks/useMessageListWidthChange';
import { NotificationMessageList } from '@/components/Channel/components/MessageItem/index';
import BotDefaultMessage from '@/components/Channel/components/BotDefaultMessage';
import { isInSmartAssitantantFullPage, isInSmartAssitantantHalfPage } from '@/utils/utils';
import gotoFirstMsgIcon from '@/assets/smartAssistant/gotoFirstMsgIcon.svg';
import { useVirtuosoContainerHeight } from '@/hooks/useVirtuosoContainerHeight';
import { v4 as uuidv4 } from 'uuid';
import classNames from 'classnames';
import { AILoadingText } from '@/utils/constants';
import MessageItem from '../MessageItem';
import MessageInput from '../MessageInput';
import MessageListForeword from '../MessageListForeword';
import styles from './index.less';
import MessageUp from '../MessageUp';
import TypingStatusComponent from './TypingStatusComponent';
import GuideQuestions from '../MessageItem/StreamMessage/GuideQuestions';
import GotoFirstUnReadBtn from './GotoFirstUnReadBtn';
import MultiSelectBottom from './MultiSelectBtn';

interface MessageListProp  {
  conversation: ConversationItem;
  inRightThread?: boolean;
  isInSmartAssistantPage?: boolean
}

const MessageList = ({ conversation, inRightThread = false, isInSmartAssistantPage = false }: MessageListProp) => {
  
  const virtuoso = useRef<VirtuosoHandle>(null);

  const [showHeader, setShowHeader] = useState(false);

  const currentConversation = conversation;

  const { throttleCheckConversationState } =
    useConversationState(currentConversation);

  const latestUnreadCount = useLatest(currentConversation?.unreadCount ?? 0);
  const groupAtType = useLatest(currentConversation?.groupAtType ?? 0);

  const { markConversationMessageAsRead } =
    useConversationSettings(currentConversation);

  const [atBottomState, setAtBottomState] = useState(false);
  const atBottomStateChangeStamp = useRef<number>(0);
  
  const {
    conversationID,
    loadState,
    moreOldLoading,
    moreOldLoadingReverse,
    getMoreOldMessages,
    getMoreOldMessagesReverse,
  } = useHistoryMessageList(currentConversation);

  const [showVirtuosoLisAfterDelay, setShowVirtuosoLisAfterDelay] =
    useState(false);

  const { messageList } = loadState;

  const flistUseConversation = useConversationStore(
    (state) => state.currentConversation
  );

  
  const currentMemberInGroup = useConversationStore(
    (state) => state.currentMemberInGroup
  );

  const currentGroupInfo = useConversationStore(
    (state) => state.currentGroupInfo
  );

  const currentMemberInGroupIniting = useConversationStore(
    (state) => state.currentMemberInGroupIniting
  );
  const { questions, clientMsgId } = useConversationStore(
    (state) => state.llmQuestions
  );
  const isMultiSession = useConversationStore((state) => state.isMultiSession);
  const currentBotConfig = useConversationStore(
    (state) => state.currentBotConfig
  );

  const currentMessageUpInfo = useConversationStore(
    (state) => state.currentMessageUpInfo
  );

  const getCurrentMessageUpInfo = useConversationStore(
    (state) => state.getCurrentMessageUpInfo
  );

  const multiSelectState = useMultiSelectStore(
    (state) => state.multiSelectState
  );

  const changeCurMessageListLength = useSmartAssitantStore((state) => state.changeCurMessageListLength)

  const isThread = useMemo(() => {
    return (
      currentConversation?.groupID && currentConversation?.parentId != null
    );
  }, [currentConversation]);

  // 是否退群或解散群聊
  const isExit = useMemo(() => {
    return (
      (currentConversation?.groupID &&
        (!currentMemberInGroup || !currentMemberInGroup.userID) &&
        !currentMemberInGroupIniting) ||
      false
    );
  }, [currentConversation, currentMemberInGroup, currentMemberInGroupIniting]);

  // 群禁言 禁用输入框相关操作
  const disabledBtn = useMemo(() => {
    return (
      (currentConversation?.groupID &&
        currentGroupInfo?.status === GroupStatus.Muted &&
        currentMemberInGroup?.roleLevel === GroupMemberRole.Normal) ||
      false
    );
  }, [currentGroupInfo, currentMemberInGroup]);

  const markConversationAsRead = useCallback(() => {
    setTimeout(() => {
      if (latestUnreadCount.current != null && latestUnreadCount.current > 0 || groupAtType.current !== 0) {
        throttleCheckConversationState();
      }
    }, 0);
  }, [throttleCheckConversationState, groupAtType]);

  const {
    hasScrolled,
    scrollToBottom,
    scrollToBottomSmooth,
    handleMessagesChange,
    handleNearBottom,
    scrollToLastMsg
  } = useMessageScroll({
    virtuosoRef: virtuoso,
    conversation: currentConversation,
    onMarkAsRead: markConversationAsRead,
  });
  const isFirstLoadRef = useRef(true);

  // 更新消息列表的 effect
  const processedMessages = useMemo(() => {
    try {
      const baseMessages = messageList;

      if (baseMessages != null) {
        return baseMessages;
      }
      return null;
    } catch (error) {
      console.error(error);
      return null;
    }
  }, [currentConversation?.conversationID, messageList]);

  const targetMsg = useConversationStore((state) => state.targetMsg);
  const updateTargetMsg = useConversationStore((state) => state.updateTargetMsg);
  

  // 感觉没啥意义？
  useDeepCompareEffect(() => {
    if (isFirstLoadRef.current) {
      if (!targetMsg.seq) {
        // scrollToBottomSmooth();
      } else {
        updateTargetMsg({});
      }
      isFirstLoadRef.current = false;
      // updateTargetMsg({});
    }
  }, [loadState.messageList?.length, conversation?.conversationID, targetMsg]);

  useDeepCompareEffect(() => {
    const handleScrollToBottom = ({
      conversation: targetConv = currentConversation,
    }) => {
      if (targetConv?.conversationID === currentConversation?.conversationID) {
        scrollToBottomSmooth();
      }
    };

    emitter.on('CHAT_LIST_SCROLL_TO_BOTTOM', handleScrollToBottom);
    return () => {
      emitter.off('CHAT_LIST_SCROLL_TO_BOTTOM', handleScrollToBottom);
    };
  }, [currentConversation]);

  useDeepCompareEffect(() => {
    if (processedMessages == null || processedMessages?.length === 0) {
      return;
    }
    // 系统消息不算是自己发送的消息
    const isSelfMessage =
      processedMessages[processedMessages.length - 1]?.sendID ===
      useUserStore.getState().selfInfo.userID && !NotificationMessageList.includes(processedMessages[processedMessages.length - 1].contentType);
    handleMessagesChange(processedMessages, isSelfMessage);
    // 智能助理需要
    if (isInSmartAssistantPage) {
      changeCurMessageListLength(processedMessages?.length || 0)
    }
  }, [processedMessages?.length, handleMessagesChange]);

  const syncState = useUserStore((state) => state.syncState);
  const connectState = useUserStore((state) => state.connectState);

  const loadMoreMessage = useCallback(
    throttle(
      () => {
        if (!loadState.hasMoreOld || moreOldLoading) {
          return;
        }
        console.debug('查询更多');
        getMoreOldMessages();
      },
      200,
      { leading: true, trailing: false }
    ),
    [loadState.hasMoreOld, moreOldLoading, getMoreOldMessages]
  );

  const loadMoreMessageReverse = useCallback(
    throttle(
      () => {
        if (!loadState.hasMoreOldReverse || moreOldLoadingReverse) {
          return;
        }
        getMoreOldMessagesReverse();
      },
      200,
      { leading: true, trailing: false }
    ),
    [
      loadState.hasMoreOldReverse,
      moreOldLoadingReverse,
      getMoreOldMessagesReverse,
    ]
  );

  const updateChannelHeaderCurTabAndConversation = useConversationStore(
    (state) => state.updateChannelHeaderCurTabAndConversation
  );

  const ref = useResizeObserverWithThreshold<HTMLDivElement>(
    () => {
      if (atBottomState || (!atBottomState && ((Date.now() - atBottomStateChangeStamp.current) < 300))) {
        virtuoso.current?.scrollToIndex({
          index: 'LAST',
          align: 'end',
          behavior: 'smooth',
        })
      }
    },
    {
      debounceDelay: 50,
      widthThreshold: 5, // 至少变化 5px 才响应
    }
  )

  const itemContentWrap = useCallback(
    (index: number, msg: MessageItemType) => {
      try {
        const isSender = useUserStore.getState().selfInfo.userID === msg.sendID
        const messageItemProps = {
          isThread,
          inRightThread, // 原样透传，重新发送消息的时候会用到
          conversationID,
          message: msg,
          messageUpdateFlag: msg.senderNickname + msg.senderFaceUrl,
          isSender,
          showName: currentConversation?.showName,
          // scrollToBottomSmooth: () => scrollToBottomSmooth(),
          currentConversation,
          // hasScrolled,
          virtuoso: virtuoso.current,
          messageList,
          hasForwardBtn: true,
        };
        const messageIndex = processedMessages?.findIndex(
          (item) => item.clientMsgID === msg.clientMsgID
        ) || 0

        return (
          <>
            <MessageItem
              hasMoreMessageBefore={loadState.hasMoreOld}
              messageIndex={messageIndex}
              isLastMsg={processedMessages ? messageIndex === processedMessages.length - 1 : false}
              prevMsg={processedMessages && messageIndex > 0 ? processedMessages?.[messageIndex - 1] : undefined}
              clientMsgId={msg.clientMsgID}
              sendID={msg.sendID}
              key={msg.clientMsgID}
              {...messageItemProps}
              isExit={isExit}
              markConversationMessageAsRead={markConversationMessageAsRead}
            />
            {msg.clientMsgID === clientMsgId && !!questions?.length && <GuideQuestions questions={questions} currentConversation={currentConversation} />}
            {isMultiSession && !isEmpty(currentBotConfig) && <div className={styles.lastMultiSessionItem}></div>}
          </>
        );
      } catch (e) {
        console.error('渲染消息报错', e);
        return <></>;
      }
    },
    [
      messageList,
      isThread,
      conversationID,
      currentConversation,
      // scrollToBottomSmooth,
      loadState.hasMoreOld,
    ]
  );

  const isConnecting = useMemo(() => {
    return (
      syncState === 'success' &&
      (connectState === 'loading' || connectState === 'failed')
    );
  }, [syncState, connectState]);

  const showNewWorkErrorInfo = useMemo(() => {
    return (
      loadState.initLoading &&
      (processedMessages == null || processedMessages?.length === 0) &&
      isConnecting
    );
  }, [loadState, processedMessages, isConnecting]);


  const showVirtuoso = !loadState.initLoading && processedMessages != null;

  // Header组件的显示时机要注意下，如果是无会话记录的新用户，直接展示；否则，在第一页历史消息查询完、rangeChanged后才应该触发，否则Header会先于虚拟列表中的消息item先出现、导致闪烁效果
  useEffect(() => {
    if (processedMessages?.length === 0 && showVirtuoso) {
      setShowHeader(true);
    }
  }, [processedMessages, showVirtuoso]);

  useEffect(() => {
    if (showVirtuoso) {
      // virtuoso.current?.autoscrollToBottom()
      setTimeout(() => setShowVirtuosoLisAfterDelay(true), 80);
    }
  }, [showVirtuoso]);

  // 缓存下
  const renderMessageUpInfo = useMemo(() => {
    if (
      currentMessageUpInfo.filter(
        (i) => i.msgs?.groupID === currentConversation?.groupID
      )?.length > 0 && !isExit
    ) {
      return (
        <div className={styles.messageUpWrap}>
          <MessageUp
            currentMessageUpInfo={currentMessageUpInfo}
            messageList={messageList}
            virtuoso={virtuoso.current}
          />
        </div>
      );
    }
    return <></>;
  }, [currentMessageUpInfo, messageList, isExit]);

  useEffect(() => {
    if (!isExit && currentConversation?.groupID) {
      getCurrentMessageUpInfo(currentConversation?.groupID)
    }
  }, [isExit, currentConversation?.groupID])

  const emptyPlaceholder = useCallback(() => {
    return <MessageListForeword
      key={flistUseConversation?.conversationID}
      isGroup={!isEmpty(flistUseConversation?.groupID)}
      currentConversation={flistUseConversation}
      sourceFormEmpty={true}
    />
  }, [flistUseConversation])

  
  const setCurrentMessageInputValue = useConversationStore(
    (state) => state.setCurrentMessageInputValue
  );

  const minHeight = useVirtuosoContainerHeight('messageList', showVirtuoso) || 0;

  useEffect(() => {
    if (isExit) {
      try {
        if (showVirtuoso && currentConversation?.conversationID != null && currentConversation?.draftText != null && currentConversation?.draftText !== "") {

          setCurrentMessageInputValue(null);
          IMSDK.setConversationDraft({
            conversationID,
            draftText: '',
          });
          
        }
      } catch (e) {
        console.error(currentConversation?.conversationID, '已退群或解散群清除草稿失败')
      }
    }
  }, [isExit, showVirtuoso, currentConversation])

  const currentMultiSession = useConversationStore(
    (state) => state.currentMultiSession
  );
  
  // 会话的lastMsg是否在当前虚拟列表的data里，用于区分是正常滚动到上方，还是从记录跳过来（只有target的上下文）
  const isConversationLastMsgInList = () => {
    if (processedMessages == null) {return false;}
    const currentListLastMsgID = processedMessages[processedMessages?.length - 1]?.clientMsgID;
    const latestMsgObj = JSON.parse(currentConversation.latestMsg);
    const { clientMsgID: currentConversationLastMsgID } = latestMsgObj;
    if (currentListLastMsgID === currentConversationLastMsgID) {return true;}
    else {return false;}

  }
  // 两种情况：1、如果当前List的最后一条是会话的lastMsg，说明是正常滚动到上面的，直接滚到底部就好；2、如果不是，说明是定位到原消息跳转到列表中部的，要直接reset使在底部
  const handleUnReadInSmartClicked = () => {
    if (isConversationLastMsgInList()) {
      scrollToBottom();
    }
    else {
      updateChannelHeaderCurTabAndConversation(
        'message',
        {
          clientMsgID: null,
          seq: null,
        },
        {
          ...currentMultiSession,
          ex: `${uuidv4()}`,
        }
      );
    }
  }

  console.debug("{processedMessages}", processedMessages);
  
  const getLastUuidGroup = (processedMessages: MessageItemType[]) => {
    if (!processedMessages?.length) {return null;}

    const selfUserID = useUserStore.getState().selfInfo.userID;
    const lastIndex = processedMessages.length - 1;
    const lastMsg = processedMessages[lastIndex];

    console.debug({lastMsg})
    if (!lastMsg) {return null;}

    let targetUuid: string | null = null;
    let scanFrom = lastIndex;


    console.debug("{ lastMsg }",lastMsg.sendID === selfUserID && lastMsg?.uuid)
    
    // --- Step 1: 判断最后一条是否是用户消息 ---
    if (lastMsg.sendID === selfUserID && lastMsg?.uuid) {
      targetUuid = lastMsg?.uuid;
    } else {
    // --- Step 2: 判断最后一条是否是流式消息 ---
      let usePrevUuid = false;
      try {
        const content = JSON.parse(lastMsg?.customElem?.data || "{}");
        if (
          content?.type === "stream"
        ) {
        // --- Step 2a: 处理 50ms 内的 loading 情况 ---
          if (
            !lastMsg.uuid &&
          lastIndex > 0
          ) {
            const prev = processedMessages[lastIndex - 1];
            if (
              prev?.sendID === selfUserID &&
            prev?.uuid &&
            lastMsg.sendTime - prev.sendTime <= 50
            ) {
              targetUuid = prev?.uuid;
              scanFrom = lastIndex - 1; // 从 prev 开始扫描
            } else {
              usePrevUuid = true;
            }
          } else {
            usePrevUuid = true;
          }
        }
      } catch (e) {
      // 忽略解析错误
      }

      if (!targetUuid && usePrevUuid) {
        if (lastIndex < 1) {return null;} // 没有倒数第二条
        const prev = processedMessages[lastIndex - 1];
        if (!prev?.uuid) {return null;}
        targetUuid = prev.uuid;
        scanFrom = lastIndex - 1;
      }

      if (!targetUuid && lastMsg.uuid) {
        targetUuid = lastMsg.uuid;
      }
    }

    if (!targetUuid) {return null;}

    // --- Step 3: 从 scanFrom 开始往前扫描同 uuid ---
    while (scanFrom >= 0 && processedMessages[scanFrom]?.uuid === targetUuid) {
      scanFrom--;
    }

    const startIndex = scanFrom + 1;
    const endIndex = lastIndex;
    const group = processedMessages.slice(startIndex, endIndex + 1);

    return {
      uuid: targetUuid,
      startIndex,
      endIndex,
      group,
    };
  };

  // ready后通过事件总线通知外部页面
  useEffect(() => {
    if (isInSmartAssistantPage && showVirtuosoLisAfterDelay) {
      emit('EXTERNAL_SMART_PAGE_LOADED');
    }
  },[isInSmartAssistantPage,showVirtuosoLisAfterDelay])
  
  return (
    !isInSmartAssistantPage ? (<div className={styles.messageWarp} key={conversationID} ref={ref}>
      {renderMessageUpInfo}
      <div className={styles.messageContent}>
        <div className={styles.box}>
          <>
            {showNewWorkErrorInfo && (
              <div className={styles.netErrorArea}>
                <img src={offlineIcon}></img>
                <span>网络已断开</span>
              </div>
            )}
            {showVirtuoso && (
              <Virtuoso
                style={{
                  position: 'absolute',
                  width: '100%',
                  visibility: showVirtuosoLisAfterDelay ? 'visible' : 'hidden',
                }}
                // logLevel={LogLevel.DEBUG}
                id={'messageList'}
                className={styles.virtuosoListContainer}
                followOutput={(isAtBottom) => {
                  if (document.hidden || !isAtBottom) {
                    return false;
                  }
                  return 'smooth';
                }}
                skipAnimationFrameInResizeObserver={true}
                firstItemIndex={loadState.firstItemIndex}
                initialTopMostItemIndex={{
                  index: loadState.initialTopMostItemIndex,
                  align: 'end',
                }}
                startReached={() => {
                  // if (targetMsg?.seq != null) {
                  //   return;
                  // }
                  loadMoreMessage();
                }}
                // onScroll={handleScroll}
                endReached={() => {
                  // if (targetMsg?.seq != null) {
                  //   return;
                  // }
                  loadMoreMessageReverse();
                }}
                ref={virtuoso}
                data={processedMessages}
                computeItemKey={(_, item) => item.clientMsgID}
                increaseViewportBy={{ top: 1500, bottom: 1200 }}
                defaultItemHeight={200}
                itemContent={itemContentWrap}
                components={{
                  Header: () => {
                    if (!showHeader) {
                      return <></>;
                    }
                    return (
                      <div
                        style={{
                          position: 'fixed',
                          left: '50%',
                          display: 'flex',
                          justifyContent: 'center',
                          padding: '5px 0',
                          visibility: loadState.hasMoreOld
                            ? 'visible'
                            : 'hidden',
                        }}
                      >
                        <LoadingSpinner />
                      </div>
                    );
                  },
                  EmptyPlaceholder: emptyPlaceholder,

                }}
                atBottomStateChange={(atBottomState: boolean) => {
                  setAtBottomState(atBottomState);
                  handleNearBottom(atBottomState);
                  if (atBottomState) { atBottomStateChangeStamp.current = Date.now(); }
                }}
                // onScroll={() => {
                //   console.debug('hasReachedBottomInitially','滚走')
                //   hasReachedBottomInitially.current=false
                // }}
                rangeChanged={(range) => {
                  console.info({ range });
                  if (showHeader) {
                    return;
                  }

                  if (range.startIndex !== range.endIndex) {
                    setShowHeader(true);
                  } else if (
                    range.startIndex === range.endIndex &&
                    range.endIndex === 9999 &&
                    processedMessages.length === 1
                  ) {
                    setShowHeader(true);
                  }
                }}
              />
            )}
            {hasScrolled && latestUnreadCount?.current != null && latestUnreadCount?.current > 0 && (
              <div
                className={styles.unReadCount}
                onClick={scrollToBottomSmooth}
              >
                最新消息
                <img src={doubleDownArrowIcon} />
              </div>
            )}
            <GotoFirstUnReadBtn />
          </>
        </div>
      </div>
      {multiSelectState ? <MultiSelectBottom currentConversation={currentConversation} /> : <>
        {isExit || disabledBtn ? (
          <div className={styles.exitBox}>
            <img src={isExit ? exitIcon : disableContactIcon} />
            <span>{isExit ? '你已退出该群聊，无法发送消息' : '已禁言'}</span>
          </div>
        ) : (
          <div className={styles.messageInputWrap}>
            <TypingStatusComponent conversation={currentConversation} />
            <MessageInput
              conversation={conversation}
              inRightThread={inRightThread}
              multiSessionDataIsEmpty={processedMessages?.length === 0}
            />
          </div>
        )}
      </>}
    </div>)
      :
      (<div className={styles.messageWarp} style={{ width: '100%', backgroundColor: '#fff', justifyContent: 'center' }} key={conversationID} ref={ref}>
        {showVirtuoso &&
        <>
          < div
            className={classNames(isInSmartAssitantantHalfPage() ? styles.inHalfSmart : "", styles.messageContent)}
            style={isInSmartAssitantantFullPage() && isEmpty(processedMessages) ? { height: '370px', flex: 'none' } : {}}
          >
            <div className={styles.box}>
              <>
                {showNewWorkErrorInfo && (
                  <div className={styles.netErrorArea}>
                    <img src={offlineIcon}></img>
                    <span>网络已断开</span>
                  </div>
                )}
                <Virtuoso
                  style={{
                    position: 'absolute',
                    width: '100%',
                    visibility: showVirtuosoLisAfterDelay ? 'visible' : 'hidden',
                  }}
                  id={'messageList'}
                  className={styles.virtuosoListContainerSmart}
                  followOutput={(isAtBottom) => {
                    if (document.hidden || !isAtBottom) {
                      return false;
                    }
                    return 'smooth';
                  }}
                  skipAnimationFrameInResizeObserver={true}
                  firstItemIndex={loadState.firstItemIndex}
                  initialTopMostItemIndex={{
                    index: loadState.initialTopMostItemIndex,
                    align: 'end',
                  }}
                  startReached={() => {
                    loadMoreMessage();
                  }}
                  endReached={() => {
                    loadMoreMessageReverse();
                  }}
                  ref={virtuoso}
                  data={processedMessages}
                  computeItemKey={(_, item) => item.clientMsgID}
                  increaseViewportBy={{ top: 1500, bottom: 1200 }}
                  defaultItemHeight={200}
                  // itemContent={itemContentWrap}
                  itemContent={(index: number, msg: MessageItemType) => {
                    const messageIndex = processedMessages.findIndex(
                      (item) => item.clientMsgID === msg.clientMsgID
                    );
                    if (messageIndex === -1) { return null; }

                    const lastGroup = getLastUuidGroup(processedMessages);

                 
                    // 没有 group，走正常逻辑
                    if (!lastGroup) {
                      return <div key={`${msg.clientMsgID}`} style={{height:"100%" }}> {itemContentWrap(index, msg) }</div>;
                    }

                    const { uuid, startIndex, endIndex, group } = lastGroup;
                    console.debug({lastGroup},messageIndex,messageIndex === startIndex ,endIndex-startIndex)
                    
                    // 如果是 group 的起点：渲染整个分组
                    if (messageIndex === startIndex && endIndex - startIndex >= 0){

                      console.debug("msg.clientMsgID", msg.clientMsgID,endIndex,startIndex,endIndex-startIndex,(endIndex-startIndex)===0?(minHeight/2):minHeight);
                      return (
                        <div key={msg.clientMsgID} style={{height:"100%", minHeight}} id={uuid}>
                          {group.map((m, idx) => itemContentWrap(index + idx, m))}
                        </div>
                      );
                    }

                    
                    // 如果在 group 内部（但不是起点）：占位
                    if (messageIndex > startIndex && messageIndex <= endIndex && endIndex - startIndex > 0) {
                      return <div key={`${msg.clientMsgID}`}  id={uuid} style={{ height: 1 }} />;
                    }

                    // 其它消息正常渲染
                    return <div key={`${msg.clientMsgID}`}   id={uuid} style={{height:"100%"}}> {itemContentWrap(index, msg) }</div>;
                  }
                  }
                  components={{ 
                    Header: () => {
                      if (!showHeader) {
                        return <></>;
                      }
                      return (
                        <div
                          style={{
                            position: 'fixed',
                            left: '50%',
                            display: 'flex',
                            justifyContent: 'center',
                            padding: '5px 0',
                            visibility: loadState.hasMoreOld
                              ? 'visible'
                              : 'hidden',
                          }}
                        >
                          <LoadingSpinner />
                        </div>
                      );
                    },
                    EmptyPlaceholder: () => {
                      return <BotDefaultMessage
                        currentConversation={conversation} />
                    },

                  }}
                  atBottomStateChange={(atBottomState: boolean) => {
                    setAtBottomState(atBottomState);
                    handleNearBottom(atBottomState);
                    if (atBottomState) { atBottomStateChangeStamp.current = Date.now(); }
                  }}
                  rangeChanged={(range) => {
                    console.info({ range });
                    if (showHeader) {
                      return;
                    }

                    if (range.startIndex !== range.endIndex) {
                      setShowHeader(true);
                    } else if (
                      range.startIndex === range.endIndex &&
                    range.endIndex === 9999 &&
                    processedMessages.length === 1
                    ) {
                      setShowHeader(true);
                    }
                  }}
                />
                {/* {hasScrolled && ( */}
                {processedMessages.length > 0 && <div
                  className={styles.unReadInSmart}
                  style={{opacity:hasScrolled?1:0,pointerEvents:hasScrolled?'auto':'none'}}
                  onClick={()=>handleUnReadInSmartClicked()}
                >
                  <img src={gotoFirstMsgIcon} />
                </div>}
                
                <GotoFirstUnReadBtn />
              </>
            </div>
          </div> 

          <div className={styles.messageInputWrap} style={{ padding: '0' }}>
            <TypingStatusComponent conversation={currentConversation} />
            <MessageInput
              conversation={conversation}
              inRightThread={inRightThread}
              multiSessionDataIsEmpty={processedMessages?.length === 0}
            />
          </div>

        </>
        }
      </div>)
  );
};

export default memo(MessageList);
