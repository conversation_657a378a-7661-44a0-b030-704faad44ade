/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
/* eslint-disable indent */
import { FC, useCallback, useEffect, useRef, useState } from 'react';
import { throttle } from 'lodash';
import classNames from 'classnames';
import { Tooltip, Upload } from '@ht/sprite-ui';
import { CloseCircleFilled } from '@ht-icons/sprite-ui-react';
import ImgPreviewModal from '@/components/ImgPreviewModal';
import zipIcon from '@/assets/channel/messageInput/fileRender/zip.png';
import fileIcon from '@/assets/channel/messageInput/fileRender/file.png';
import arrowIcom from '@/assets/channel/messageInput/fileRender/arrow.svg';
import addBtnIcon from '@/assets/smartAssistant/addBtn.svg';
import addBtnDisabledIcon from '@/assets/smartAssistant/addBtnDisabled.svg';
import arrowBtnIcon from '@/assets/smartAssistant/arrowBtn.svg';
import dayjs from 'dayjs';
import {
  getDocIcon,
  docTypeEnum,
  isInSmartAssitantantPage,
  isInSmartAssitantantHalfPage,
} from '@/utils/utils';
import { openImgPreviewWin } from '@/utils/message';
import { bytesToSize } from '@/utils/common';
import { FileListType } from '.';
import { switchType } from './SmartAssistantInputRender';
import styles from './index.less';

export const getFileIcon = (fileName: string, type: 'icon' | 'text') => {
  let result;
  const idx = fileName.lastIndexOf('.');
  const fileType = fileName.slice(idx + 1).toLowerCase();
  switch (fileType) {
    case 'doc':
      result = type === 'icon' ? getDocIcon(docTypeEnum.WORD) : 'Word 文档';
      break;
    case 'docx':
      result = type === 'icon' ? getDocIcon(docTypeEnum.WORD) : 'Word 文档';
      break;
    case 'xls':
      result =
        type === 'icon' ? getDocIcon(docTypeEnum.EXCEL) : 'Excel 电子表格';
      break;
    case 'csv':
      result =
        type === 'icon' ? getDocIcon(docTypeEnum.EXCEL) : 'Excel 电子表格';
      break;
    case 'xlsx':
      result =
        type === 'icon' ? getDocIcon(docTypeEnum.EXCEL) : 'Excel 电子表格';
      break;
    case 'ppt':
      result = type === 'icon' ? getDocIcon(docTypeEnum.PPT) : 'PPT';
      break;
    case 'pptx':
      result = type === 'icon' ? getDocIcon(docTypeEnum.PPT) : 'PPT';
      break;
    case 'pdf':
      result = type === 'icon' ? getDocIcon(docTypeEnum.PDF) : 'PDF';
      break;
    case 'xmind':
      result = type === 'icon' ? getDocIcon(docTypeEnum.MINDMAPNEW) : 'xmind';
      break;
    case 'md':
      result = type === 'icon' ? getDocIcon(docTypeEnum.MARKDOWN) : 'md';
      break;
    case 'zip':
      result = type === 'icon' ? zipIcon : '压缩包';
      break;
    case 'rar':
      result = type === 'icon' ? zipIcon : '压缩包';
      break;
    default:
      result = type === 'icon' ? fileIcon : fileType;
  }
  return result;
};

interface FileRenderProps {
  fileList: FileListType[];
  setFileList: (value: FileListType[]) => void;
  retryUploadFile?: (fileId: string) => void;
  uploadMsg?: (data: any, type: string, isCapture?: boolean) => void;
  typeTab?: string;
  isDisabledAdd?: boolean;
  maxCount?: number;
}

const FileRender: FC<FileRenderProps> = ({
  fileList,
  setFileList,
  retryUploadFile,
  uploadMsg,
  typeTab = '',
  isDisabledAdd = false,
  maxCount = 10,
}) => {
  const [open, setOpen] = useState<boolean>(false);
  const [fileData, setFileData] = useState<any>();
  const [hasScroll, setHasScroll] = useState<boolean>(false);
  const [showScroll, setShowScroll] = useState<'left' | 'right' | 'all'>(
    'right'
  );
  const fileRef = useRef<any>(null);
  const prevFileListRef = useRef(fileList);

  useEffect(() => {
    setShowScroll('right');
  }, []);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      entries.forEach((entry) => {
        const hasScroll = entry.target.scrollWidth > entry.target.clientWidth;
        setHasScroll(hasScroll);
        if (!hasScroll) {
          setShowScroll('right');
        }
      });
    });
    if (fileRef.current) {
      observer.observe(fileRef.current);
    }
    if (fileList.length > prevFileListRef.current.length) {
      scrollToRightEnd();
    }
    // 更新ref为当前值
    prevFileListRef.current = fileList;

    return () => {
      if (fileRef.current) {
        observer.unobserve(fileRef.current);
      }
    };
  }, [fileList]);

  const previewImg = useCallback(async (data: FileListType) => {
    if (isInSmartAssitantantHalfPage()) {
      return;
    }
    const { width, height } = await getPicInfo(data.url);
    setFileData({
      width,
      height,
      url: data.url,
      fileName: data?.file?.name || '',
    });
    openImgPreviewWin(
      {
        width,
        height,
        url: data.url,
        fileName: data?.file?.name || '',
      },
      () => {
        setOpen(true);
      }
    );
  }, []);

  const previewImgThrottle = throttle(previewImg, 300, {
    leading: false,
    trailing: true,
  });

  const getPicInfo = (url: string): Promise<HTMLImageElement> =>
    new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = function () {
        resolve(img);
      };
      img.src = url;
    });

  const removeItem = (data: FileListType) => {
    const newFileData = fileList.filter((item) => item.id !== data.id);
    setFileList(newFileData);
  };

  const leftArrow = () => {
    const nowScroll = fileRef.current.scrollLeft;
    if (nowScroll - 200 <= 0) {
      fileRef.current.scrollTo({
        left: 0,
        behavior: 'smooth',
      });
      setShowScroll('right');
    } else {
      fileRef.current.scrollTo({
        left: nowScroll - 200,
        behavior: 'smooth',
      });
      setShowScroll('all');
    }
  };

  const rightArrow = () => {
    const nowScroll = fileRef.current.scrollLeft;
    const { scrollWidth, clientWidth } = fileRef.current;
    if (nowScroll + 200 >= scrollWidth - clientWidth) {
      fileRef.current.scrollTo({
        left: scrollWidth,
        behavior: 'smooth',
      });
      setShowScroll('left');
    } else {
      fileRef.current.scrollTo({
        left: nowScroll + 200,
        behavior: 'smooth',
      });
      setShowScroll('all');
    }
  };

  const scrollToRightEnd = () => {
    const { scrollWidth, clientWidth } = fileRef.current;

    fileRef.current.scrollTo({
      left: scrollWidth - clientWidth,
      behavior: 'smooth',
    });
    setShowScroll('left');
  };
  const processedCount = useRef(0);

  const renderAddBtn = () => {
    return (
      <Upload
        accept={switchType(typeTab)}
        action={''}
        customRequest={(data) => {
          if (processedCount.current + 1 <= maxCount) {
            uploadMsg?.(data, typeTab === 'doc' ? 'file' : typeTab);
            processedCount.current += 1;
          }
        }}
        showUploadList={false}
        multiple={true}
        disabled={isDisabledAdd}
      >
        <div
          className={classNames(
            styles.addBtnWrap,
            isDisabledAdd && styles.addBtnWrap_disabled
          )}
          onClick={() => (processedCount.current = 0)}
        >
          <img src={isDisabledAdd ? addBtnDisabledIcon : addBtnIcon} />
        </div>
      </Upload>
    );
  };

  return (
    <div
      className={classNames(
        styles.fileRenderWarp,
        isInSmartAssitantantPage() && styles.fileRenderWarp_smartAssitantant
      )}
    >
      <div className={styles.fileRenderContent} ref={fileRef}>
        {fileList.map((item) => {
          if (item.isImage) {
            return (
              <div
                key={item.id}
                className={classNames(
                  styles.fileImgItem,
                  isInSmartAssitantantHalfPage() && styles.autoCursor
                )}
                onClick={() => previewImgThrottle(item)}
              >
                <div>
                  <img src={item.url} />
                  {/* 显示上传状态 */}
                  {item.uploadStatus === 'uploading' && (
                    <div className={styles.imgProgressBar}>
                      <div className={styles.imgProgress}></div>
                    </div>
                  )}
                  {item.uploadStatus === 'failed' && (
                    <div
                      className={`${styles.uploadStatus} ${styles.failed}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        retryUploadFile?.(item.id);
                      }}
                    >
                      <div className={styles.failedText}>点击重试</div>
                    </div>
                  )}
                  {item.uploadStatus === 'success' && (
                    <div
                      className={`${styles.uploadStatus} ${styles.success}`}
                    ></div>
                  )}
                </div>
                <div
                  className={styles.closeIocn}
                  onClick={(e) => {
                    e.stopPropagation();
                    removeItem(item);
                  }}
                >
                  <Tooltip title={'移除文件'}>
                    <CloseCircleFilled />
                  </Tooltip>
                </div>
              </div>
            );
          } else if (item.isClouDoc) {
            return (
              <div className={styles.cloudDocItm} key={item.id}>
                <div className={styles.icon}>
                  <img src={getDocIcon(item.docInfo.documentType)} />
                </div>
                <div className={styles.fileInfo}>
                  <div
                    className={styles.fileName}
                    title={item.docInfo.documentName}
                  >
                    {item.docInfo.documentName}
                  </div>
                  <div className={styles.info}>
                    <span>创建者:</span>
                    <span>{item.docInfo.createdBy}</span>
                    <div className={styles.line}></div>
                    <span>创建时间:</span>
                    <span
                      style={{ textOverflow: 'ellipsis', overflow: 'hidden' }}
                      title={dayjs(item.docInfo.createdTime).format(
                        'YYYY-MM-DD HH:mm'
                      )}
                    >
                      {dayjs(item.docInfo.createdTime).format(
                        'YYYY-MM-DD HH:mm'
                      )}
                    </span>
                  </div>
                </div>
                <div
                  className={styles.closeIocn}
                  onClick={(e) => {
                    e.stopPropagation();
                    removeItem(item);
                  }}
                >
                  <Tooltip title={'移除文件'}>
                    <CloseCircleFilled />
                  </Tooltip>
                </div>
              </div>
            );
          } else {
            return (
              <div
                className={`${styles.fileItem} ${
                  item.uploadStatus === 'failed' ? styles.fileItemFailed : ''
                }`}
                key={item.id}
              >
                {item.uploadStatus === 'uploading' && (
                  <div className={styles.progressBar}>
                    <div className={styles.progress}></div>
                  </div>
                )}
                <div
                  className={`${styles.icon} ${
                    item.uploadStatus === 'uploading' ||
                    item.uploadStatus === 'failed'
                      ? styles.iconFaded
                      : ''
                  }`}
                >
                  <img src={getFileIcon(item?.file?.name || '', 'icon')} />
                </div>
                <div className={styles.fileInfo}>
                  <div
                    className={`${styles.fileName} ${
                      item.uploadStatus === 'uploading' ||
                      item.uploadStatus === 'failed'
                        ? styles.textFaded
                        : ''
                    }`}
                    title={item?.file?.name}
                  >
                    {item?.file?.name}
                  </div>
                  <div className={styles.info}>
                    {item.uploadStatus === 'failed' && (
                      <div className={styles.failedInfo}>
                        <span className={styles.failedText}>上传失败，</span>
                        <span
                          className={styles.retryText}
                          onClick={(e) => {
                            e.stopPropagation();
                            retryUploadFile?.(item.id);
                          }}
                        >
                          点击重试
                        </span>
                      </div>
                    )}
                    {(item.uploadStatus === 'success' ||
                      !item.uploadStatus) && (
                      <span>
                        {isInSmartAssitantantPage()
                          ? bytesToSize(item?.file?.size || 0)
                          : getFileIcon(item?.file?.name || '', 'text')}
                      </span>
                    )}
                  </div>
                </div>
                <Tooltip title={'移除文件'}>
                  <div
                    className={`${styles.closeIocn} ${
                      item.uploadStatus === 'uploading' ||
                      item.uploadStatus === 'failed'
                        ? styles.iconFaded
                        : ''
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      removeItem(item);
                    }}
                  >
                    <CloseCircleFilled />
                  </div>
                </Tooltip>
              </div>
            );
          }
        })}
        {isInSmartAssitantantPage() && renderAddBtn()}
      </div>
      {hasScroll && (showScroll === 'left' || showScroll === 'all') && (
        <div className={styles.leftArrow}>
          <img
            src={isInSmartAssitantantPage() ? arrowBtnIcon : arrowIcom}
            onClick={leftArrow}
          />
        </div>
      )}
      {hasScroll && (showScroll === 'right' || showScroll === 'all') && (
        <div className={styles.rightArrow}>
          <img
            src={isInSmartAssitantantPage() ? arrowBtnIcon : arrowIcom}
            onClick={rightArrow}
          />
        </div>
      )}
      {open && (
        <ImgPreviewModal
          open={open}
          onClose={() => setOpen(false)}
          download={() => {}}
          imgInfo={fileData}
          isInput={true}
          fileName={fileData?.fileName || ''}
          openView={() => {}}
        />
      )}
    </div>
  );
};

export default FileRender;
