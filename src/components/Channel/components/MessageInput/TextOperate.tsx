import { FC, useContext } from 'react';
import boldIcon from '@/assets/channel/messageInput/menu/bold.svg';
import italicIcon from '@/assets/channel/messageInput/menu/italic.svg';
import throughIcon from '@/assets/channel/messageInput/menu/through.svg';
import linkIcon from '@/assets/channel/messageInput/menu/link.svg';
import orderIcon from '@/assets/channel/messageInput/menu/order.svg';
import disorderIcon from '@/assets/channel/messageInput/menu/disorder.svg';
import replyIcon from '@/assets/channel/messageInput/menu/reply.svg';
import {
  toggleStrongCommand,
  toggleEmphasisCommand,
  // linkSchema,
  blockquoteSchema,
  bulletListSchema,
  orderedListSchema,
} from '@milkdown/preset-commonmark';
import { CmdKey, commandsCtx, editorViewCtx } from '@milkdown/core';
import { toggleStrikethroughCommand } from '@milkdown/kit/preset/gfm';
import classNames from 'classnames';
import { Tooltip } from '@ht/sprite-ui';
import { EditorContext } from '@/components/MdEditor/editorContent';
import { changeNodeTypePreserveContent } from '@/components/MdEditor/util';
import { linkSchema } from '@/components/MdEditor/plugin-link';
import { linkTooltipAPI } from '@/components/Crepe/components/link-tooltip';
import styles from './index.less';

interface TextOperateProps {
  showFormater: boolean;
  disabledBtn: boolean;
  activeMarks?: Set<string>;
}
const TextOperate: FC<TextOperateProps> = ({
  showFormater,
  disabledBtn,
  activeMarks,
}) => {
  const { editorInstance } = useContext(EditorContext);

  const handle = (key: CmdKey<unknown>) => {
    if (!editorInstance?.editor) {
      return;
    }

    try {
      const { editor } = editorInstance;
      const commands = editor.ctx.get(commandsCtx);

      commands.call(key);
    } catch (error) {
      console.error('Failed:', error);
    }
  };

  const nodeHandle = (type: any) => {
    if (!editorInstance?.editor) {
      return;
    }

    try {
      const { editor } = editorInstance;
      const view = editor.ctx.get(editorViewCtx);
      const { dispatch, state } = view;

      const command = changeNodeTypePreserveContent(type(editor.ctx));
      command(state, dispatch);
      view.focus();
    } catch (error) {
      console.error('Failed', error);
    }
  };

  const handleLink = () => {
    if (!editorInstance?.editor) {
      return;
    }
    const { editor } = editorInstance;
    const view = editor.ctx.get(editorViewCtx);
    const { selection } = view.state;
    const {
      state: { doc },
    } = view;
    // If the selection has a link mark, remove it.
    const hasLinkMark = selection.$from
      .marks()
      .some((mark: any) => mark.type === linkSchema.type(editor.ctx));

    if (hasLinkMark) {
      removeLink();
      return;
    }

    editor.ctx.get(linkTooltipAPI.key).addLink(selection.from, selection.to);
  };

  const removeLink = () => {
    if (!editorInstance?.editor) {
      return;
    }

    const { editor } = editorInstance;
    const view = editor.ctx.get(editorViewCtx);
    const { selection } = view.state;

    editor.ctx.get(linkTooltipAPI.key).removeLink(selection.from, selection.to);
  };

  const textOperateList = [
    {
      idx: 0,
      title: '加粗',
      icon: boldIcon,
      type: 'bold',
      onClick: () => {
        handle(toggleStrongCommand.key);
      },
    },
    {
      idx: 1,
      title: '倾斜',
      icon: italicIcon,
      type: 'italic',
      onClick: () => {
        handle(toggleEmphasisCommand.key);
      },
    },
    {
      idx: 2,
      title: '删除线',
      icon: throughIcon,
      type: 'strikethrough',
      onClick: () => {
        handle(toggleStrikethroughCommand.key);
      },
    },
    {
      type: 'divider',
    },
    {
      idx: 3,
      title: '链接',
      icon: linkIcon,
      type: 'link',
      onClick: () => {
        handleLink();
      },
    },
    {
      idx: 4,
      title: '有序列表',
      icon: orderIcon,
      type: 'order',
      onClick: () => {
        nodeHandle(orderedListSchema.type);
      },
    },
    {
      idx: 5,
      title: '无序列表',
      icon: disorderIcon,
      type: 'disorder',
      onClick: () => {
        nodeHandle(bulletListSchema.type);
      },
    },
    {
      type: 'divider',
    },
    {
      idx: 6,
      title: '引用',
      icon: replyIcon,
      type: 'reply',
      onClick: () => {
        nodeHandle(blockquoteSchema.type);
      },
    },
    // {
    //   idx: 7,
    //   title: '代码块',
    //   icon: codeIcon,
    //   onClick: () => {
    //     nodeHandle(codeBlockSchema.type);
    //   },
    // },
    // {
    //   idx: 8,
    //   title: '代码',
    //   icon: codeTagIcon,
    //   onClick: () => {
    //     handle(toggleInlineCodeCommand.key);
    //   },
    // },
  ];
  return (
    <div
      className={styles.textOperate}
      style={showFormater ? {} : { width: '0', overflow: 'hidden' }}
      onClick={(e) => e.stopPropagation()}
    >
      {textOperateList.map((item, index) => {
        if (item.type && item.type === 'divider') {
          // eslint-disable-next-line react/no-array-index-key
          return <div key={`${index}divider`} className={styles.line}></div>;
        } else {
          const isActive = activeMarks?.has(item.type);
          return (
            <div
              key={item.idx}
              onClick={() => {
                if (item.onClick && !disabledBtn) {
                  item?.onClick();
                }
              }}
              onMouseDown={(e) => e.preventDefault()}
              className={classNames(
                styles.operateItem,
                disabledBtn && styles.operateItemdisabled,
                isActive && styles.activeIcon
              )}
            >
              <div
                className={classNames(
                  disabledBtn && styles.operateItemdisabled
                )}
              >
                <Tooltip
                  title={item.title ? item.title : null}
                  overlayClassName={styles.tooltipWrap}
                >
                  <img src={item.icon} alt={item.title} />
                </Tooltip>
              </div>
            </div>
          );
        }
      })}
    </div>
  );
};

export default TextOperate;
