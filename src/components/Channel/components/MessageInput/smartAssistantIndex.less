@import "src/pages/smartAssistantHalfPage/breakpoint.less";

.smartAssistantInputRenderWrap {
  width: 100%;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: end;

  .emptyWarning {
    width: 132px;
    height: 40px;
    background: #ffffff;
    box-shadow: 0 2px 12px 0 rgba(13, 26, 38, 12%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: calc(50% - 66px);
    top: 10px;
    z-index: 990;

    > img {
      margin-right: 8px;
      width: 16px;
      height: 16px;
    }
  }

  .typeGroupWrap {
    // &.inHalfSmart {
    //   @media (max-height: @half-collapse-breakpoint2) {
    //     display: none;
    //   }
    // }

    display: flex;
    align-items: center;
  }
  @keyframes plate-background-blur-on-expand {
    0% {
      filter: blur(0);
    }
    11% {
      filter: blur(5px);
    }
    100% {
      filter: blur(0);
    }
  }

  @keyframes gradient-border-width {
    0% {
      border-width: 0;
    }

    11% {
      border-width: 2px;
    }
    100% {
      border-width: 0;
    }
  }

  @keyframes gradient-spin-expand {
    0% {
      opacity: 0;
      transform: rotate(90deg);
    }

    11% {
      opacity: 1;
    }
    100% {
      opacity: 0;
      transform: rotate(250deg);
    }
  }

  @keyframes color-pulse-on-expand {
    11% {
      background: #ffffff;
    }
  }

  .inputWrap {
    // background: transparent;
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 4%);
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    padding: 0 0 12px;
    min-height: 46px;
    // max-height: 46px;
    // max-width: 46px;
    overflow: hidden;
    position: relative;
    transition: min-height 0.3s;
    &.expanded {
      min-height: 118px;
    }
    .glowContainer.active {
      contain: paint;
      inset: 0;
      position: absolute;
      &::before {
        animation: gradient-spin-expand 0.9s cubic-bezier(0.2, 0, 0, 1);
        content: "";
        position: absolute;
        opacity: 0;
        background: conic-gradient(
            rgba(52, 168, 82, 0%) 0deg,
            rgba(52, 168, 82, 100%) 38.9738deg,
            rgba(255, 211, 20, 100%) 62.3678deg,
            rgba(255, 70, 65, 100%) 87.0062deg,
            rgba(49, 134, 255, 100%) 107.428deg,
            rgba(49, 134, 255, 50%) 204.48deg,
            rgba(49, 134, 255, 0%) 308.88deg,
            rgba(52, 168, 82, 0%) 360deg
          )
          border-box;
        left: 50%;
        // z-index: 999;
        top: 50%;
        translate: -50% -50%;
        width: 100vw;
        height: 700px;
        // scale: 1 0.4;
        pointer-events: none;
        transition: opacity 0.3s;
        filter: blur(20px);
      }
      &::after {
        animation: color-pulse-on-expand 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: inherit;
        content: "";
        position: absolute;
        inset: 0;
        background: #ffffff;
        z-index: -1;
      }
    }
    .borderAnimation.active {
      animation: gradient-border-width 0.9s cubic-bezier(0.4, 0, 0.2, 1);
      position: absolute;
      contain: paint;
      border-radius: inherit;
      inset: 0;
      border: 0 solid transparent;

      &::before {
        content: "";
        contain: paint;
        position: absolute;
        inset: 0;
        border-radius: inherit;
        filter: blur(0);
        transition: filter 0.6s;
        background-color: #ffffff;
        animation: plate-background-blur-on-expand 0.9s
            cubic-bezier(0.4, 0, 0.2, 1),
          color-pulse-on-expand 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }

    // &.inHalfSmart {
    //   @media (max-height: @half-collapse-breakpoint2) {
    //     padding: 0;
    //     min-height: 36px;
    //     max-height: 36px;
    //     flex-direction: row;
    //   }
    // }

    display: flex;
    flex-direction: column;

    .textWarp {
      position: relative;
      margin-top: 12px;
      overflow-y: auto;
      max-height: 220px;
      margin-bottom: 44px;

      // &.inHalfSmart {
      //   @media (max-height: @half-collapse-breakpoint2) {
      //     margin: 0;
      //   }
      // }

      .placeholder {
        font-size: 14px;
        color: #999999;
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        position: absolute;
        top: 0;
        left: 12px;
      }
    }

    .textWarp_fullScreen {
      max-height: 168px;
      // min-height: 37px;
    }

    .questionTipWrap {
      padding-top: 12px;
      display: flex;
      align-items: center;
      padding-left: 12px;
      z-index: 15;

      // &.inHalfSmart {
      //   @media (max-height: @half-collapse-breakpoint2) {
      //     display: none;
      //   }
      // }
      .question {
        display: flex;
        align-items: center;
        border-radius: 8px;
        border: 1px solid #eaeaea;
        padding: 7px 12px;
        margin-right: 8px;
        cursor: pointer;

        > span {
          font-size: 14px;
          color: #1d222c;
          line-height: 18px;
          margin-right: 6px;
        }

        &:hover {
          background: #f2f2f3;
        }
      }
    }
  }

  // .editContainer {
  //   &.inHalfSmart {
  //     @media (max-height: @half-collapse-breakpoint2) {
  //       flex: 1;
  //       padding-top: 8px;
  //       padding-bottom: 8px;
  //     }
  //   }
  // }
  .footer {
    position: absolute;
    bottom: 12px;
    right: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-right: 12px;
    z-index: 15;
    transition: bottom 0.3s;

    &.inHalfSmart {
      width: 100%;
    }

    // &.expanded {
    //   bottom: 10px;
    // }

    .footerBtn {
      width: 32px;
      height: 32px;
      display: flex;
      // &.inHalfSmart {
      // @media (max-height: @half-collapse-breakpoint2) {
      //   &:nth-child(1) {
      //     margin-left: 0;
      //   }
      // }

      // width: 24px;
      // height: 24px;
      // }
      > img {
        width: 100%;
        height: 100%;
      }

      &.sendBtn {
        margin-left: 8px;
      }
    }

    .searchBtn {
      width: 96px;
      height: 32px;
      border-radius: 8px;
      opacity: 1;
      border: 1px solid #dddee0;
      margin-left: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #1d222c;
      line-height: 22px;
      position: absolute;
      left: 12px;
      top: 0;

      > img {
        margin-right: 4px;
      }

      &.actived {
        background: rgba(41, 98, 255, 5%);
        border: 1px solid rgba(41, 98, 255, 20%);
        color: #2962ff;
      }
    }

    .canSend {
      cursor: pointer;
    }

    .recordingArea {
      width: 32px;
      height: 32px;
      // &.inHalfSmart {
      //   @media (max-height: @half-collapse-breakpoint2) {
      //     width: 24px;
      //     height: 24px;
      //   }
      // }

      background: #2962ff;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;

      > div:nth-child(1) {
        margin-left: 8px;
      }
      > div:not(:nth-child(1)) {
        margin-left: 2px;
      }
      > div:last-child {
        margin-right: 8px;
      }
    }

    .recordingDot {
      width: 4px;
      height: 4px;
      background: #ffffff;
      border-radius: 100%;
      animation: fade 1.5s ease-in-out;

      &:nth-child(2) {
        animation-delay: 0.5s;
      }
      &:nth-child(3) {
        animation-delay: 1s;
      }
    }

    @keyframes fade {
      0% {
        opacity: 0.8;
      }
      50% {
        opacity: 0.5;
      }
      100% {
        opacity: 0.2;
      }
    }
  }
}

.typeTooltip {
  :global {
    .linkflow-tooltip-inner {
      box-shadow: 0 2px 12px 0 rgba(10, 34, 83, 12%);
      border-radius: 8px;
      padding: 12px;
      font-size: 12px;
      color: #3f434b;
      line-height: 18px;
    }
  }
}
