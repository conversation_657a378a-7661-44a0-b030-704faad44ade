/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable complexity */
/* eslint-disable max-statements */
/* eslint-disable max-lines */
/* eslint-disable react/boolean-prop-naming */
import {
  useState,
  useImperativeHandle,
  forwardRef,
  useRef,
  useEffect,
  useMemo,
} from 'react';
import classNames from 'classnames';
import { isEmpty } from 'lodash';
import { Tooltip, Upload, message as Message } from '@ht/sprite-ui';
import { useConversationStore, useUserStore } from '@/store';
import imageIcon from '@/assets/smartAssistant/image.svg';
import imageActivedIcon from '@/assets/smartAssistant/imageActived.svg';
import fileIcon from '@/assets/smartAssistant/file.svg';
import fileActivedIcon from '@/assets/smartAssistant/fileActived.svg';
import recordingIcon from '@/assets/smartAssistant/recording.svg';
import sendIcon from '@/assets/smartAssistant/send.svg';
import sendActivedIcon from '@/assets/smartAssistant/sendActived.svg';
import stopIcon from '@/assets/smartAssistant/stop.svg';
import questionArrowIcon from '@/assets/smartAssistant/questionArrow.svg';
import oaSearchIcon from '@/assets/smartAssistant/oaSearch.svg';
import oaSearchActivedIcon from '@/assets/smartAssistant/oaSearchActived.svg';
import { EditMd } from '@/components/MdEditor';
import { EditorProvider } from '@/components/MdEditor/editorContent';
import { ProsemirrorAdapterProvider } from '@prosemirror-adapter/react';
import {
  isInSmartAssitantantFullPage,
  isInSmartAssitantantHalfPage,
} from '@/utils/utils';
import { feedbackToast } from '@/utils/common';
import emptyInputIcon from '@/assets/smartAssistant/emptyInput.svg';
import { LinkflowInputRenderProps } from './LinkflowInputRender';
import FileRender from './FileRender';
import styles from './smartAssistantIndex.less';

export const switchType = (type: string) => {
  switch (type) {
    case 'doc':
      return '.pdf,.xls,.csv,.xlsx,.doc,.docx';
    case 'image':
      return '.jpg,.jpeg,.png';
    default:
      return '*';
  }
};

interface SmartAssistantInputRenderProps {
  botExConfig?: any;
}
const SmartAssistantInputRender = (
  {
    paste,
    handleKeyDown,
    typing,
    value,
    hasInit,
    draftText,
    setTypingStatus,
    grouMemberList,
    editMdRef,
    setValue,
    handleCompositionStart,
    handleCompositionEnd,
    uploadMsg,
    conversation,
    cloudUpload,
    fileList,
    setFileList,
    retryUploadFile,
    handleCanSend,
    handleSendBtn,
    botExConfig,
    enterToSend,
  }: LinkflowInputRenderProps & SmartAssistantInputRenderProps,
  SmartAssistantInputRenderRef: any
) => {
  const [recordStatus, setRecordStatus] = useState<boolean>(false); // 是否正在语音输入
  const [inputFocused, setInputFocused] = useState<boolean>(false); // 是否正在输入
  const [showEmptyVoiceInputWarning, setShowEmptyVoiceInputWaring] =
    useState<string>(''); // 是否显示说话太短的提示

  const inputContainerRef = useRef<HTMLDivElement | null>(null);

  const { userID } = useUserStore.getState().selfInfo;
  const defaultType =
    localStorage.getItem(
      `smartAssistant.messageInput.questionType.${userID}`
    ) || '';
  const [isOaSearch, setIsOaSearch] = useState<boolean>(
    localStorage.getItem(`smartAssistant.messageInput.oaSearch.${userID}`) ===
      '1'
  );
  const TYPE_GROUP = [
    {
      key: 'image',
      icon: imageIcon,
      activedIcon: imageActivedIcon,
      title: (
        <>
          <span style={{ marginBottom: '4px' }}>图片识别</span>
          <br />
          最多上传{botExConfig ? botExConfig.maxFileNum : 10}个，每个不超过
          {botExConfig ? botExConfig.maxFileSize / 1024 : 10}MB
        </>
      ),
      label: '图片识别',
    },
    {
      key: 'doc',
      icon: fileIcon,
      activedIcon: fileActivedIcon,
      title: (
        <>
          <span style={{ marginBottom: '4px' }}>文档阅读</span>
          <br />
          最多上传{botExConfig ? botExConfig.maxFileNum : 10}个，每个不超过
          {botExConfig ? botExConfig.maxFileSize / 1024 : 10}MB，
          <br />
          支持pdf、excel，word格式
        </>
      ),
      label: '文档阅读',
    },
  ];
  const placeholderText = '请输入您要咨询的问题，通过Ctrl+Enter换行';

  const QUESTION_TIPS = [
    {
      key: 1,
      type: 'doc',
      label: '总结文档',
    },
    {
      key: 2,
      type: 'doc',
      label: '翻译文档',
    },
    {
      key: 3,
      type: 'image',
      label: '概括图片内容',
    },
    {
      key: 4,
      type: 'image',
      label: '提取文字',
    },
  ];

  const llmLoading = useConversationStore((state) => state.llmLoading);

  const ref = useRef<HTMLDivElement>(null);
  const typeTab = useRef<any>(defaultType);

  const [showPlaceHolder, setShowPlaceHolder] = useState(false);
  useEffect(() => {
    if (!typing && value === '') {
      setShowPlaceHolder(true);
    } else {
      setShowPlaceHolder(false);
    }
  }, [typing, value]);

  useEffect(() => {
    if (!ref.current || !paste) {
      return;
    }

    const handler = (e: Event) => {
      setShowPlaceHolder(false);
      editMdRef.current?.setValue(e?.detail?.messageText);

      scrollToBottomAfterParse();
    };
    ref.current.addEventListener('parse', handler);

    return () => {
      ref.current?.removeEventListener('parse', handler);
    };
  }, [paste]);

  const handleTypeChange = (type: string) => {
    if (typeTab.current === type) {
      typeTab.current = '';
      editMdRef.current?.focus();

      localStorage.setItem(
        `smartAssistant.messageInput.questionType.${userID}`,
        ''
      );
    } else {
      typeTab.current = type;
      editMdRef.current?.focus();
      localStorage.setItem(
        `smartAssistant.messageInput.questionType.${userID}`,
        type
      );
    }
    setFileList([]);
    processedCount.current = 0;
  };

  const scrollToBottomAfterParse = () => {
    if (ref?.current) {
      ref.current.scrollTo({
        top: ref.current.scrollHeight,
        behavior: 'smooth',
      });
    }
  };

  const renderTypeTab = (item: any) => {
    return (
      <Tooltip
        title={item.title}
        overlayClassName={styles.typeTooltip}
        color={'#fff'}
        destroyTooltipOnHide={true}
      >
        <div
          className={classNames(
            styles.footerBtn,
            styles.canSend,
            isInSmartAssitantantHalfPage() && styles.inHalfSmart
          )}
          onClick={() => handleTypeChange(item.key)}
        >
          <img
            src={typeTab.current === item.key ? item.activedIcon : item.icon}
          />
        </div>
      </Tooltip>
    );
  };

  useImperativeHandle(
    SmartAssistantInputRenderRef,
    () => ({
      typeTab: typeTab.current,
      isOaSearch,
    }),
    [typeTab.current, isOaSearch] // 监听不可拿掉
  );

  const isDisabledAdd = useMemo(() => {
    // 检查文件数量限制
    if (botExConfig && fileList.length + 1 > botExConfig.maxFileNum) {
      return true;
    }
    return false;
  }, [botExConfig, fileList.length]);

  const maxCount = useMemo(() => {
    return botExConfig ? botExConfig.maxFileNum - fileList.length : 10;
  }, [botExConfig, fileList.length]);

  const checkFileType = (fileItem: any) => {
    // 检查文件类型
    const allowedTypes = switchType(typeTab.current).split(',');
    const fileExtension = fileItem.name.split('.').pop()?.toLowerCase();
    const isValidType = allowedTypes.includes(`.${fileExtension}`);
    if (!isValidType) {
      Message.error(`暂不支持上传此类文件格式：${fileExtension}`);
    }
    return isValidType;
  };

  const handleUploadMsg = (data: any, type: string, isCapture?: boolean) => {
    if (checkFileType(data.file)) {
      uploadMsg?.(data, type);
    }
  };

  const timeRef = useRef<any>();
  useEffect(() => {
    if (showEmptyVoiceInputWarning !== '') {
      timeRef.current = setTimeout(() => {
        setShowEmptyVoiceInputWaring('');
        if (timeRef.current) {
          clearInterval(timeRef.current);
        }
      }, 2 * 1000);
    }
  }, [showEmptyVoiceInputWarning]);

  useEffect(() => {
    return () => {
      if (timeRef.current) {
        clearInterval(timeRef.current);
      }
    };
  }, []);

  const beforeStartRecord = () => {
    if (timeRef.current) {
      clearInterval(timeRef.current);
    }
    setShowEmptyVoiceInputWaring('');
  };

  const processedCount = useRef(0);

  useEffect(() => {
    if (!inputContainerRef.current) {
      return;
    }

    const ro = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { height } = entry.contentRect;

        // 通知父页面
        if (window.parent !== window) {
          window.parent.postMessage(
            { type: 'inputContainerResize', data: height },
            '*'
          );
        }
      }
    });
    ro.observe(inputContainerRef.current);

    return () => {
      ro.disconnect();
    };
  });

  useEffect(() => {
    const editMd = editMdRef.current;
    if (editMd == null) {
      return;
    }
    const onListenParentInput = (event: MessageEvent) => {
      // 可选：检查来源，保证安全
      // if (event.origin !== location.origin) {
      //   return;
      // }

      const { data } = event;

      // 根据 type 处理不同消息
      if (data.type === 'parentInput') {
        setShowPlaceHolder(false);
        if (editMdRef.current) {
          editMdRef.current?.setValue(data.data);
        }

        scrollToBottomAfterParse();
      } else if (data.type === 'parentSend') {
        setShowPlaceHolder(false);
        editMdRef.current?.setValue(data.data);

        handleSendBtn(data.data);
      }
    };

    window.addEventListener('message', onListenParentInput);

    // 组件卸载时清理监听
    return () => {
      window.removeEventListener('message', onListenParentInput);
    };
  }, [editMdRef, handleSendBtn]);

  return (
    <div
      className={classNames(styles.smartAssistantInputRenderWrap)}
      tabIndex={-1}
      onFocus={(e) => {
        console.log('smartAssistantInputContainer focused', e);

        setInputFocused(true);
      }}
      onBlur={(e) => {
        console.log('smartAssistantInputContainer blured', e);
        setInputFocused(false);
      }}
    >
      {showEmptyVoiceInputWarning !== '' && (
        <div className={styles.emptyWarning}>
          <img src={emptyInputIcon}></img>
          <span>
            {showEmptyVoiceInputWarning === 'empty'
              ? '未识别到文字'
              : '转文字失败'}
          </span>
        </div>
      )}

      <EditorProvider>
        <div
          ref={inputContainerRef}
          id="smartAssistantInputContainer"
          className={classNames(
            styles.inputWrap,
            isInSmartAssitantantHalfPage() && styles.inHalfSmart,
            inputFocused ? styles.expanded : ''
          )}
          onClick={(e) => {
            editMdRef.current?.focus();
            if (isInSmartAssitantantHalfPage()) {
              e?.stopPropagation();
              e.preventDefault();
            }
          }}
        >
          <div
            className={classNames(
              styles.glowContainer,
              inputFocused ? styles.active : ''
            )}
          ></div>
          <div
            className={classNames(
              styles.borderAnimation,
              inputFocused ? styles.active : ''
            )}
          ></div>
          {!isEmpty(typeTab.current) && (
            <div className={styles.fileOrImageWrap}>
              <FileRender
                fileList={
                  fileList?.length > 10 ? fileList.slice(0, 10) : fileList
                }
                setFileList={setFileList}
                retryUploadFile={retryUploadFile}
                uploadMsg={handleUploadMsg}
                typeTab={typeTab.current}
                isDisabledAdd={isDisabledAdd}
                maxCount={maxCount}
              />
            </div>
          )}
          {!isEmpty(typeTab.current) && fileList.length > 0 && (
            <div
              className={classNames(
                styles.questionTipWrap,
                isInSmartAssitantantHalfPage() && styles.inHalfSmart
              )}
            >
              {QUESTION_TIPS.map((listItem) => {
                return (
                  listItem.type === typeTab.current && (
                    <div
                      key={listItem.key}
                      className={styles.question}
                      onClick={(e) => {
                        enterToSend(listItem.label);
                        e.stopPropagation();
                      }}
                    >
                      <span>{listItem.label}</span>
                      <img src={questionArrowIcon} />
                    </div>
                  )
                );
              })}
            </div>
          )}
          <div
            className={classNames(
              styles.editContainer,
              isInSmartAssitantantHalfPage() && styles.inHalfSmart
            )}
          >
            <div
              ref={ref}
              className={classNames(
                styles.textWarp,
                isInSmartAssitantantHalfPage() && styles.inHalfSmart,
                isInSmartAssitantantFullPage() && styles.textWarp_fullScreen
              )}
              onPaste={paste}
              onKeyDown={handleKeyDown}
              id="pasteContainerId"
              spellCheck={false}
            >
              {showPlaceHolder && (
                <div className={styles.placeholder}>{placeholderText}</div>
              )}
              {
                <ProsemirrorAdapterProvider>
                  {hasInit && (
                    <EditMd
                      defaultValue={draftText?.value || draftText}
                      onFocus={() => {
                        setTypingStatus(true);
                      }}
                      onBlur={() => {
                        setTypingStatus(false);
                      }}
                      groupMemberList={grouMemberList}
                      ref={editMdRef}
                      onChange={(val: string) => {
                        setValue(val);
                      }}
                      onCompositionStart={handleCompositionStart}
                      onCompositionEnd={handleCompositionEnd}
                      showFormater={false}
                      setShowFormater={() => {}}
                      uploadMsg={uploadMsg}
                      conversationID={conversation?.conversationID}
                      cloudUpload={cloudUpload}
                      isGroup={!!conversation?.groupID}
                      isMultiModalBotConv={false}
                      isBot={true}
                      inputStyle={{
                        padding: '0 12px',
                      }}
                      editorContainerStyle={{
                        padding: '0',
                      }}
                      setShowEmptyVoiceInputWaring={() =>
                        setShowEmptyVoiceInputWaring('empty')
                      }
                      beforeStartRecord={beforeStartRecord}
                      // setShowVoiceInputError={() =>
                      //   setShowEmptyVoiceInputWaring('transFail')
                      // }
                      setShowPlaceHolder={setShowPlaceHolder}
                      scrollToBottomAfterParse={scrollToBottomAfterParse}
                    />
                  )}
                </ProsemirrorAdapterProvider>
              }
            </div>
          </div>
          <div
            className={classNames(
              styles.footer,
              inputFocused ? styles.expanded : '',
              isInSmartAssitantantHalfPage() && styles.inHalfSmart
            )}
          >
            {isInSmartAssitantantHalfPage() && (
              <div
                className={classNames(
                  styles.searchBtn,
                  styles.canSend,
                  isOaSearch && styles.actived
                )}
                onClick={() => {
                  if (isOaSearch) {
                    localStorage.removeItem(
                      `smartAssistant.messageInput.oaSearch.${userID}`
                    );
                    if (window.parent !== window) {
                      window.parent.postMessage(
                        {
                          type: 'smartInputChanged',
                          data: '',
                        },
                        '*'
                      );
                    }
                  } else {
                    localStorage.setItem(
                      `smartAssistant.messageInput.oaSearch.${userID}`,
                      '1'
                    );
                    if (window.parent !== window) {
                      window.parent.postMessage(
                        {
                          type: 'smartInputChanged',
                          data: value,
                        },
                        '*'
                      );
                    }
                  }

                  setIsOaSearch(!isOaSearch);
                }}
              >
                <img src={isOaSearch ? oaSearchActivedIcon : oaSearchIcon} />
                搜索推荐
              </div>
            )}

            <div className={classNames(styles.typeGroupWrap)}>
              {TYPE_GROUP.map((typeItem) => {
                return <>{renderTypeTab(typeItem)}</>;
              })}
            </div>
            {recordStatus ? (
              // <Tooltip title={'停止语音输入'}>
              <Tooltip title={'语音功能敬请期待'}>
                <div
                  className={classNames(
                    styles.footerBtn,
                    styles.canSend,
                    styles.recordingArea,
                    isInSmartAssitantantHalfPage() && styles.inHalfSmart
                  )}
                  onClick={(e) => {
                    feedbackToast({
                      error: '语音功能敬请期待',
                      msg: '语音功能敬请期待',
                    });
                    // const result = editMdRef?.current.endRecord();
                    // if (result) {
                    //   setRecordStatus(false);
                    // }
                    // e?.stopPropagation();
                  }}
                >
                  <div className={styles.recordingDot}></div>
                  <div className={styles.recordingDot}></div>
                  <div className={styles.recordingDot}></div>
                </div>
              </Tooltip>
            ) : (
              // <Tooltip title={'开始语音输入'}>
              <Tooltip title={'语音功能敬请期待'}>
                <div
                  className={classNames(
                    styles.footerBtn,
                    styles.canSend,
                    isInSmartAssitantantHalfPage() && styles.inHalfSmart
                  )}
                  onClick={(e) => {
                    feedbackToast({
                      error: '语音功能敬请期待',
                      msg: '语音功能敬请期待',
                    });
                    // const result = editMdRef?.current.startRecord();
                    // if (result) {
                    //   setRecordStatus(true);
                    // }
                  }}
                >
                  <img src={recordingIcon} />
                </div>
              </Tooltip>
            )}
            <div
              className={classNames(
                styles.footerBtn,
                styles.sendBtn,
                handleCanSend ? styles.canSend : styles.cannotSend,
                isInSmartAssitantantHalfPage() && styles.inHalfSmart
              )}
              onClick={(e) => {
                handleSendBtn();
                e.stopPropagation();
              }}
            >
              {llmLoading ? (
                <img src={stopIcon} />
              ) : (
                <img src={handleCanSend ? sendActivedIcon : sendIcon} />
              )}
            </div>
          </div>
        </div>
      </EditorProvider>
    </div>
  );
};

export default forwardRef(SmartAssistantInputRender);
