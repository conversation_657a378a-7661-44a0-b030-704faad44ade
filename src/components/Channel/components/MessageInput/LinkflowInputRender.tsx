/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
/* eslint-disable react/boolean-prop-naming */
import { FC, useRef, useState, useMemo } from 'react';
import classNames from 'classnames';
import { isEmpty } from 'lodash';
import {
  BotCommandItem,
  GroupMemberItem,
  ConversationItem,
  MessageItem,
} from '@ht/openim-wasm-client-sdk';
import { useConversationStore } from '@/store';
import { useRobot } from '@/hooks/useRobot';
import { EditorProvider } from '@/components/MdEditor/editorContent';
import { EditMd } from '@/components/MdEditor';
import RobotConfigModal from '@/components/Channel/components/MessageListForeword/RobotConfigModal';
import QuoteMessageRender from '@/components/Channel/components/MessageItem/QuoteMessageRender';
import { ProsemirrorAdapterProvider } from '@prosemirror-adapter/react';
import configSetIcon from '@/assets/channel/robotConversationList/configSet.svg';
import quoteDeleteIcon from '@/assets/channel/messageInput/quoteDelete.svg';
import stopIcon from '@/assets/channel/messageInput/stop.svg';
import sendIcon from '@/assets/channel/messageInput/send.png';
import { DraftTextProps, editMdRefType, FileListType } from '.';
import FileRender from './FileRender';
import { RobotCommand } from './RobotCommand';
import TextOperate from './TextOperate';
import styles from './index.less';

export interface LinkflowInputRenderProps {
  isMultiSessionCreate: boolean;
  isMultiModalBotConv: boolean;
  showMultiSessionSet: boolean;
  paste: (e: any) => void;
  handleKeyDown: (e: any) => void;
  selectedCommand: BotCommandItem | null;
  typing: boolean;
  value: string;
  setValue: (val: string) => void;
  handleTopNumber: () => string;
  placeholderText: string;
  hasInit: boolean;
  draftText: DraftTextProps | undefined;
  setTypingStatus: (val: boolean) => void;
  grouMemberList: GroupMemberItem[];
  editMdRef: editMdRefType;
  handleCompositionStart: () => void;
  handleCompositionEnd: () => void;
  uploadMsg?: (data: any, type: string, isCapture?: boolean) => void;
  conversation: ConversationItem | undefined;
  cloudUpload: (val: any) => void;
  isBot: boolean;
  replyMsg: MessageItem;
  setReplyMsg: (val: any) => void;
  fileList: FileListType[];
  setFileList: (val: FileListType[]) => void;
  retryUploadFile: (val: string) => void;
  showCommandList: boolean;
  setShowCommandList: (val: boolean) => void;
  setSelectedCommand: (val: BotCommandItem | null) => void;
  formInfo: DraftTextProps['formInfo'];
  setFormInfo: (val: any) => void;
  enterToSend: (val: string) => void;
  handleCanSend: boolean;
  handleSendBtn: (value?: any) => void;
}

interface robotCommandRefProps {
  manuallySubmit: () => void;
  getFormState: () => any;
}
const LinkflowInputRender: FC<LinkflowInputRenderProps> = ({
  isMultiSessionCreate,
  isMultiModalBotConv,
  showMultiSessionSet,
  paste,
  handleKeyDown,
  selectedCommand,
  typing,
  value,
  handleTopNumber,
  placeholderText,
  hasInit,
  draftText,
  setTypingStatus,
  grouMemberList,
  editMdRef,
  setValue,
  handleCompositionStart,
  handleCompositionEnd,
  uploadMsg,
  conversation,
  cloudUpload,
  isBot,
  replyMsg,
  setReplyMsg,
  fileList,
  setFileList,
  retryUploadFile,
  showCommandList,
  setShowCommandList,
  setSelectedCommand,
  setFormInfo,
  enterToSend,
  formInfo,
  handleCanSend,
  handleSendBtn,
}) => {
  const defaultFormatterDisplayPref =
    localStorage.getItem('linkim.messageInput.formatterDisplayPref') !==
    'false';

  const messageInputRef = useRef(null);
  const robotCommandRef = useRef<robotCommandRefProps>();

  const [showFormater, setShowFormater] = useState<boolean>(
    !!defaultFormatterDisplayPref
  );

  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );
  const currentMemberInGroup = useConversationStore(
    (state) => state.currentMemberInGroup
  );
  const botConfig = useConversationStore((state) => state.botConfig);
  const currentBotConfig = useConversationStore(
    (state) => state.currentBotConfig
  );
  const botConfigModal = useConversationStore((state) => state.botConfigModal);
  const updateBotConfigModal = useConversationStore(
    (state) => state.updateBotConfigModal
  );
  const llmLoading = useConversationStore((state) => state.llmLoading);

  const { submitRobotCommand } = useRobot(conversation?.groupID as string);

  const botSubmit = async (values: any, sessionType: number) => {
    const { command, ...data } = values;

    // // 将data中的数据整合为duration: 1days winners: 1 prize: 200 格式，拼接在message中,没值的就不加了
    // const message = `/${command.name} ${Object.entries(data)
    //   .filter(
    //     ([_, value]) => value !== undefined && value !== null && value !== ''
    //   )
    //   .map(([key, value]) => `${key}: ${value}`)
    //   .join(' ')}`;

    let message = `/${command.name}`;
    if (!isEmpty(data)) {
      const options: any[] = command.options || [];
      const obj: any = {};
      // eslint-disable-next-line array-callback-return
      Object.keys(data).map((key) => {
        const item = options.filter((val: any) => val.key === key);
        const label = item[0].name;
        obj[label] = data[key];
      });
      message = `/${command.name} ${JSON.stringify(obj)}`;
    }
    // 先发消息，消息成功后再发机器人消息
    try {
      enterToSend(`${message}`);
      // 发送消息成功后再调用机器人交互
      await submitRobotCommand({
        userID: currentMemberInGroup?.userID,
        groupID: conversation?.groupID,
        botID: command.botID,
        actionID: command.key,
        actionName: command.name,
        type: 1,
        sessionType,
        data,
      });
    } catch (error) {
      console.error('发送机器人指令失败:', error);
    }
  };

  // 缓存下，避免头像img不停的闪烁
  const ReplyPrefix = useMemo(() => {
    return replyMsg ? (
      <div className={styles.replyBox}>
        <div>
          <QuoteMessageRender message={replyMsg} isInput={true} />
        </div>
        <div className={styles.removeReplyMsg}>
          <img src={quoteDeleteIcon} onClick={() => setReplyMsg(undefined)} />
        </div>
      </div>
    ) : null;
  }, [replyMsg]);

  return (
    <div
      className={styles.content}
      style={
        isMultiSessionCreate
          ? { display: 'none' }
          : { minHeight: isMultiModalBotConv ? '120px' : '80px' }
      }
      ref={messageInputRef}
    >
      {showMultiSessionSet && (
        <div className={styles.robotConfigWarp}>
          <div
            className={styles.robotConfig}
            onClick={() => updateBotConfigModal(true)}
          >
            <img src={configSetIcon} />
            <span>修改对话设置</span>
          </div>
        </div>
      )}
      <EditorProvider>
        <>
          <div
            className={styles.textWarp}
            onPaste={paste}
            onKeyDown={handleKeyDown}
            id="pasteContainerId"
            style={selectedCommand ? { display: 'none' } : {}}
          >
            {!typing && value === '' && !selectedCommand && (
              <div
                style={{
                  top: handleTopNumber(),
                }}
                className={styles.placeholder}
              >
                {placeholderText}
              </div>
            )}
            {
              <ProsemirrorAdapterProvider>
                {hasInit && (
                  <EditMd
                    defaultValue={draftText?.value || draftText}
                    onFocus={() => setTypingStatus(true)}
                    onBlur={() => setTypingStatus(false)}
                    groupMemberList={grouMemberList}
                    ref={editMdRef}
                    onChange={(val: string) => {
                      setValue(val);
                    }}
                    onCompositionStart={handleCompositionStart}
                    onCompositionEnd={handleCompositionEnd}
                    showFormater={showFormater}
                    setShowFormater={setShowFormater}
                    uploadMsg={uploadMsg}
                    conversationID={conversation?.conversationID}
                    cloudUpload={cloudUpload}
                    isGroup={!!conversation?.groupID}
                    isMultiModalBotConv={isMultiModalBotConv}
                    isBot={isBot}
                  />
                )}
              </ProsemirrorAdapterProvider>
            }
          </div>
          <>{ReplyPrefix}</>
          {fileList && fileList.length > 0 && (
            <FileRender
              fileList={fileList}
              setFileList={setFileList}
              retryUploadFile={retryUploadFile}
            />
          )}
          <RobotCommand
            ref={robotCommandRef}
            showFormater={showFormater}
            value={value}
            showCommandList={showCommandList}
            setShowCommandList={setShowCommandList}
            selectedCommand={selectedCommand}
            messageInputRef={messageInputRef}
            groupId={conversation?.groupID}
            botId={conversation?.userID}
            onClose={() => {
              setShowCommandList(false);
              setSelectedCommand(null);

              setFormInfo(undefined);
            }}
            onSelected={(command) => {
              setShowCommandList(false);
              setSelectedCommand(command);
              setReplyMsg(undefined);
            }}
            onSubmit={(values) => {
              botSubmit(values, conversation?.conversationType);
              setShowCommandList(false);
              setSelectedCommand(null);
            }}
            editMdRef={editMdRef}
            setFormInfo={setFormInfo}
            formInfo={formInfo}
          />
          <div
            className={styles.footer}
            style={selectedCommand ? { display: 'none' } : {}}
          >
            <TextOperate
              showFormater={showFormater}
              disabledBtn={false}
              activeMarks={editMdRef.current?.activeMarks}
            />
            <div
              className={classNames(
                styles.sendBtn,
                handleCanSend ? styles.canSend : styles.cannotSend
              )}
              onClick={() => handleSendBtn()} // 不能缩写为onClick={handleSendBtn}
            >
              {llmLoading ? (
                <img src={stopIcon} style={{ width: '20px' }} />
              ) : (
                <img style={{ width: '16px' }} src={sendIcon} />
              )}
            </div>
          </div>
        </>
      </EditorProvider>
      {botConfigModal && (
        <RobotConfigModal
          modalOpen={botConfigModal}
          oncancel={() => updateBotConfigModal(false)}
          botConfig={botConfig}
          defaultValue={currentBotConfig?.data || {}}
          showName={currentConversation?.showName || ''}
        />
      )}
    </div>
  );
};

export default LinkflowInputRender;
