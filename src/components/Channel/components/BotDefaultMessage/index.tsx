/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable react/no-array-index-key */
import { FC } from 'react';
import classNames from 'classnames';
import { ConversationItem } from '@ht/openim-wasm-client-sdk';
import { useConversationStore } from '@/store';
import { IMSDK } from '@/layouts/BasicLayout';
import { useSendMessage } from '@/hooks/useSendMessage';
import useUserInfo from '@/hooks/useUserInfo';
import questionArrowIcon from '@/assets/smartAssistant/questionArrow.svg';
import { isInSmartAssitantantFullPage } from '@/utils/utils';
import styles from './index.less';

interface BotDefaultMessageProps {
  currentConversation?: ConversationItem;
}
const BotDefaultMessage: FC<BotDefaultMessageProps> = ({
  currentConversation,
}) => {
  const { showName = '' } = currentConversation || {};
  const botIntro = useConversationStore((state) => state.botIntro);
  const botQuestions =
    useConversationStore((state) => state.botQuestions) || [];
  const { sendStreamMessage } = useSendMessage(currentConversation);
  const { userDetail } = useUserInfo(currentConversation?.userID);

  // const BOT_QUESTION_LIST = [
  //   '今日日程总结',
  //   '今日热点资讯总结',
  //   '差旅报告查看',
  // ];

  const handleSendMsg = async (val: string) => {
    const message = (await IMSDK.createTextMessage(val)).data;

    sendStreamMessage({
      recvUser: userDetail,
      curMsg: message,
      curConversation: currentConversation,
      LinQAgentJump: '',
    });
  };

  return (
    <div
      className={classNames(
        styles.botDefaultMessageWrap,
        isInSmartAssitantantFullPage() &&
          styles.botDefaultMessageWrap_fullScreen
      )}
    >
      <div className={styles.botTitle}>嗨！我是{showName}</div>
      <div className={styles.botDesc}>
        {botIntro ? botIntro : '你可以试着问我：'}
      </div>
      {botQuestions.length > 0 && (
        <div className={styles.botQuestionList}>
          {botQuestions.map((listItem, index) => {
            return (
              <div
                key={`${index}-${listItem}`}
                className={styles.question}
                onClick={() => {
                  handleSendMsg(listItem);
                }}
              >
                <span>{listItem}</span>
                <img src={questionArrowIcon} />
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default BotDefaultMessage;
