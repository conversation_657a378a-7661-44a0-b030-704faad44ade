import { memo, FC, useMemo } from 'react';
import { useConversationStore } from '@/store';
import RenderRightList from './RenderRightList';
import { ChannelProps } from '../..';

const ChannelRightArea: FC<ChannelProps> = () => {
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );

  const isGroup = !!currentConversation?.groupID;

  const currentMemberInGroup = useConversationStore(
    (state) => state.currentMemberInGroup
  );

  // 是否退群或解散群聊
  const isExit =
    (currentConversation?.groupID &&
      (!currentMemberInGroup || !currentMemberInGroup.userID)) ||
    false;

  const ifShowMemberList = useMemo(() => {
    return isGroup && !isExit;
  }, [isExit, isGroup]);

  return <RenderRightList ifShowMemberList={ifShowMemberList} />;
};
export default memo(ChannelRightArea);
