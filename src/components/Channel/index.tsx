import { memo, FC } from 'react';
import { useConversationStore } from '@/store';
import styles from './index.less';
import ChannelRightArea from './components/ChannelRightArea';
import ChannelLeftArea from './components/ChannelLeftArea';

export type ChannelHeaderTabType =
  | 'message'
  // | 'IM'
  // | 'files'
  // | 'bookmarks'
  // | 'pins'
  | 'docs';

export interface ChannelProps {
  hasDeleteIcon?: boolean;
}

const Channel: FC<ChannelProps> = (props) => {
  const { hasDeleteIcon = false } = props;

  return (
    <div className={styles.channelWarp} data-id="channel-warp">
      <ChannelLeftArea hasDeleteIcon={hasDeleteIcon} />
      {!hasDeleteIcon && <ChannelRightArea />}
    </div>
  );
};

export default memo(Channel);
