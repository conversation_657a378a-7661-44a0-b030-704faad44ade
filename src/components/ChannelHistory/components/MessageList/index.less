.messageListWrapper {
  flex: 1;
  position: relative;
  .virtuosoListContainer {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    transform: translateZ(0);
    will-change: opacity;

    .listItemBorder {
      position: relative;
      &::after {
        content: "";
        display: block;
        width: calc(100% - 92px);
        height: 1px;
        background: rgba(0, 0, 0, 5%);
        position: absolute;
        bottom: 0;
        left: 72px;
      }

      &.isSmart {
        padding: 0 23px 0 24px;
        &::after {
          display: none;
        }
      }
    }

    > div > div > div {
      &:last-of-type {
        padding-bottom: 10px;
        .listItemBorder {
          &::after {
            display: none;
          }
        }

        .pictureMsgContent {
          margin-bottom: 0;
        }
      }
    }

    &::-webkit-scrollbar {
      width: 8px;
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: transparent;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
    }

    &:hover::-webkit-scrollbar {
      width: 8px;
    }

    &:hover::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 20%);
      border-radius: 3px;
    }

    &:hover::-webkit-scrollbar-track {
      background-color: transparent;
    }

    .pictureMsgContent {
      margin-bottom: 18px;

      .sendDate {
        font-weight: 400;
        color: #1d1c1d;
        line-height: 20px;
        padding: 0 20px 14px;
      }

      &.isSmart {
        margin-bottom: 12px;
        padding: 0 0 0 24px;

        .sendDate {
          padding: 0 0 8px;
          font-size: 12px;
          color: #6c6f76;
          line-height: 16px;
        }

        .imgListWrapper {
          padding: 0;
        }
      }
    }

    .imgListWrapper {
      display: flex;
      flex-wrap: wrap;
      padding: 0 20px;
    }

    &.isSmart {
      :global {
        .linkflow-empty-image {
          height: fit-content !important;
          margin-bottom: 8px;
        }
      }
    }
  }

  :global {
    .linkflow-empty-simple {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      padding: 0;
      margin: 0;
    }
  }
}
