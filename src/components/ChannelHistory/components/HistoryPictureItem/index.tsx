import { FC, useState, useCallback } from 'react';
import { throttle } from 'lodash';
import classNames from 'classnames';
import {
  isInSmartAssitantantHalfPage,
  isInSmartAssitantantPage,
} from '@/utils/utils';
import { Dropdown, message as Message } from '@ht/sprite-ui';
import ImgPreviewModal from '@/components/ImgPreviewModal';
import { openImgPreviewWin } from '@/utils/message';
import HistoryRightButtonMenu from '../HistoryRightButtonMenu';
import styles from './index.less';

interface HistoryPictureItemProps {
  listItem: any;
  conversationID: string;
  historyTab?: string;
}
const HistoryPictureItem: FC<HistoryPictureItemProps> = ({
  listItem,
  conversationID,
  historyTab = '',
}) => {
  const [rightBtnOpen, setRightBtnOpen] = useState(false);
  const [fileOpen, setFileOpen] = useState(false);

  const { pictureElem } = listItem;
  const { url = '', uuid } = pictureElem?.sourcePicture || {};
  const fileName = uuid?.split('/')[1] || 'image.png';

  const download = async () => {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('网络响应失败');
      }
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      URL.revokeObjectURL(blobUrl);
    } catch (error) {
      Message.error('下载失败');
    }
  };

  const openView = async () => {
    window.open(url, '_blank');
  };

  const previewImg = useCallback(async () => {
    openImgPreviewWin(
      {
        ...listItem.pictureElem?.sourcePicture,
      },
      () => {
        setFileOpen(true);
      }
    );
  }, [listItem.pictureElem?.sourcePicture]);

  const previewImgThrottle = throttle(previewImg, 300, {
    leading: false,
    trailing: true,
  });

  return (
    <div
      className={classNames(
        styles.historyPictureItemWrapper,
        isInSmartAssitantantPage() && styles.isSmart,
        isInSmartAssitantantHalfPage() && styles.isSmartHalf
      )}
    >
      <Dropdown
        overlay={
          <HistoryRightButtonMenu
            message={listItem}
            conversationID={conversationID || ''}
            setRightBtnOpen={setRightBtnOpen}
            historyTab={historyTab}
          />
        }
        trigger={['contextMenu']}
        overlayClassName={classNames(
          styles.rightButtonMenu,
          isInSmartAssitantantPage() && styles.isSmart
        )}
        open={rightBtnOpen}
        onOpenChange={(open: boolean) => {
          setRightBtnOpen(open);
        }}
      >
        <div className={styles.imgBox}>
          <img
            className={styles.showPicture}
            src={listItem.pictureElem?.sourcePicture.url}
            onClick={async () => {
              if (!isInSmartAssitantantHalfPage()) {
                previewImgThrottle();
              }
            }}
          />
        </div>
      </Dropdown>

      {fileOpen && (
        <ImgPreviewModal
          open={fileOpen}
          onClose={() => setFileOpen(false)}
          download={download}
          imgMsg={listItem}
          fileName={fileName}
          showName={listItem.showName}
          openView={openView}
        />
      )}
    </div>
  );
};

export default HistoryPictureItem;
