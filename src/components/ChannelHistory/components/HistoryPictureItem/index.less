.historyPictureItemWrapper {
  padding-bottom: 4px;

  .imgBox {
    width: 162px;
    height: 162px;
    border-radius: 9px;
    border: 1px solid rgb(198, 200, 202);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 4px;
    cursor: pointer;
    overflow: hidden;

    > img {
      max-width: 100%;
      max-height: 100%;
    }
  }

  &.isSmart {
    padding-bottom: 8px;

    .imgBox {
      width: 138px;
      height: 138px;
      margin-right: 8px;
    }

    &:nth-of-type(3n) {
      .imgBox {
        margin-right: 0;
      }
    }
  }

  &.isSmartHalf {
    .imgBox {
      width: 132px;
      height: 132px;
    }
  }
}

.rightButtonMenu {
  width: 220px;
  border-radius: 8px;
  box-shadow: 0 2px 4px 2px rgba(0, 0, 0, 4%);
  overflow: hidden;

  :global {
    .linkflow-dropdown-menu {
      padding: 6px;
    }

    .linkflow-dropdown-menu-item {
      padding: 0;
      font-size: 14px;
      // font-weight: 600;
      font-weight: 500;
      color: var(--primary-text-color-1);
      border-radius: 6px;
    }

    .linkflow-dropdown-menu-item-divider {
      margin: 3px 0;
      background-color: var(--primary-background-color-5);
    }
  }

  &.isSmart {
    width: 140px;
    box-shadow: 0 2px 12px 0 rgba(13, 26, 38, 12%);

    :global {
      .linkflow-dropdown-menu {
        padding: 4px 0;
      }

      .linkflow-dropdown-menu-item {
        border-radius: 0;
      }
    }
  }
}
