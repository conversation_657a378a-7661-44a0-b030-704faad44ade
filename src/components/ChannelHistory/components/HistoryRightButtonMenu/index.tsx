/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable indent */
/* eslint-disable max-lines */
/* eslint-disable max-statements */
import { FC, memo, useMemo, useState, useCallback } from 'react';
import { throttle } from 'lodash';
import {
  MessageItem as MessageItemType,
  MessageType,
} from '@ht/openim-wasm-client-sdk';

import classNames from 'classnames';
import { Menu, message as Message } from '@ht/sprite-ui';
import { v4 as uuidv4 } from 'uuid';
import {
  useConversationStore,
  useMultiSelectStore,
  useSmartAssitantStore,
} from '@/store';
import copyIcon from '@/assets/channel/rightButtonMenu/copy.svg';
import openIcon from '@/assets/channel/rightButtonMenu/open.svg';
import forwardIcon from '@/assets/channel/rightButtonMenu/forward.svg';
import type { MenuProps } from '@ht/sprite-ui';
import { emit } from '@/utils/events';
import { feedbackToast } from '@/utils/common';
import {
  isInSmartAssitantantHalfPage,
  isInSmartAssitantantPage,
} from '@/utils/utils';
import { handleCopy, openMultiMessageWin } from '@/utils/message';
import { IMSDK } from '@/layouts/BasicLayout';
import { deleteOneMessage } from '@/hooks/useHistoryMessageList';
import { selectedTextRef } from '@/hooks/useSelectedText';
import MultiMessageModal from '@/components/Channel/components/MultiMessageModal';
import toSessionIcon from '@/assets/channelHistory/toSession.svg';
import toSessionIcon1 from '@/assets/smartAssistant/rightButtons/toSessionIcon1.svg';
import downloadIcon from '@/assets/smartAssistant/rightButtons/download.svg';
import copyIcon1 from '@/assets/smartAssistant/rightButtons/copy.svg';
import ForwardModal from '@/components/ForwardModal';
import addAsEmojiIcon from '@/assets/channel/rightButtonMenu/addAsEmoji.svg';
import styles from './index.less';

const menus = [
  'toSession',
  'copy',
  'open',
  'forwardMsg',
  'addAsEmoji',
  'divider',
  'remove',
];

const smartMenus = ['toSession', 'downLoad', 'copy'];

interface RightButtonMenuProps {
  message: MessageItemType;
  conversationID: string;
  setRightBtnOpen: (val: boolean) => void;
  historyTab?: string;
}

const HistoryRightButtonMenu: FC<RightButtonMenuProps> = ({
  message,
  conversationID,
  setRightBtnOpen,
  historyTab = '',
}) => {
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );
  const updateTargetMsg = useConversationStore(
    (state) => state.updateTargetMsg
  );
  const getCurrentMessageUpInfo = useConversationStore(
    (state) => state.getCurrentMessageUpInfo
  );
  const updateChannelHeaderCurTabAndConversation = useConversationStore(
    (state) => state.updateChannelHeaderCurTabAndConversation
  );

  const deleteMultiSelect = useMultiSelectStore(
    (state) => state.deleteMultiSelect
  );

  const [forwardModal, setForwardModal] = useState<boolean>(false);
  const [multiMessageModalOpen, setMultiMessageModalOpen] =
    useState<boolean>(false);

  const menuButtons: any = useMemo(() => {
    const tempMenu = {
      toSession: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <img src={toSessionIcon} />
              <span>跳转至原文</span>
            </div>
          </div>
        ),
        key: 'toSession',
      },
      copy: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <img src={copyIcon} />
              <span>复制</span>
            </div>
          </div>
        ),
        key: 'copy',
      },
      open: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <img src={openIcon} />
              <span>打开</span>
            </div>
          </div>
        ),
        key: 'open',
      },
      forwardMsg: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <img src={forwardIcon} />
              <span>转发</span>
            </div>
          </div>
        ),
        key: 'forwardMsg',
      },
      addAsEmoji: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <img src={addAsEmojiIcon} />
              <span>添加到表情</span>
            </div>
          </div>
        ),
        key: 'addAsEmoji',
        disabled: message?.isFaceUrlInValid, // FaceMessageRender加载失败的时候会
      },
      divider: {
        type: 'divider',
      },
      remove: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <span>删除</span>
            </div>
          </div>
        ),
        key: 'remove',
      },
    };
    return tempMenu;
  }, []);

  const smartMenuButtons: any = useMemo(() => {
    const tempMenu = {
      toSession: {
        label: (
          <div
            className={classNames(
              styles.rightButtonMenuItem,
              isInSmartAssitantantPage() && styles.isSmart
            )}
          >
            <div>
              <img src={toSessionIcon1} />
              <span>定位至聊天</span>
            </div>
          </div>
        ),
        key: 'toSession',
      },
      downLoad: {
        label: (
          <div
            className={classNames(
              styles.rightButtonMenuItem,
              isInSmartAssitantantPage() && styles.isSmart
            )}
          >
            <div>
              <img src={downloadIcon} />
              <span>下载</span>
            </div>
          </div>
        ),
        key: 'downLoad',
      },
      copy: {
        label: (
          <div
            className={classNames(
              styles.rightButtonMenuItem,
              isInSmartAssitantantPage() && styles.isSmart
            )}
          >
            <div>
              <img src={copyIcon1} />
              <span>复制</span>
            </div>
          </div>
        ),
        key: 'copy',
      },
    };
    return tempMenu;
  }, []);

  const addAsEmoji = async () => {
    const { faceElem, pictureElem } = message;
    if (faceElem == null && pictureElem == null) {
      return;
    }
    try {
      if (faceElem != null) {
        if (faceElem.faceSize < 2 * 1024 * 1024) {
          await IMSDK.createFace({
            faceWidth: faceElem?.faceWidth,
            faceHeight: faceElem?.faceHeight,
            faceSize: faceElem?.faceSize,
            imgType: faceElem.imgType,
            faceURL: faceElem.faceURL,
            faceName: faceElem.faceName,
          });
        } else {
          Message.error('仅限选择2M以下的图片');
          return;
        }
      } else if (pictureElem != null) {
        if (pictureElem.sourcePicture.size < 2 * 1024 * 1024) {
          await IMSDK.createFace({
            faceWidth: pictureElem.sourcePicture.width,
            faceHeight: pictureElem.sourcePicture.height,
            faceSize: pictureElem.sourcePicture.size,
            imgType: pictureElem.sourcePicture.type,
            faceURL: pictureElem.sourcePicture.url,
            faceName: '自定义表情',
          });
        } else {
          Message.error('仅限选择2M以下的图片');
          return;
        }
      }
      Message.info('已添加至表情');
      emit('REFRESHEMOJIS');
    } catch (e) {
      if (e.errCode === 1801) {
        Message.error('此表情已存在，不可重复添加');
      } else {
        console.error('添加至表情失败，', { e });
        Message.error('添加至表情失败');
      }
    }
  };

  const getItems = useMemo(() => {
    let items: MenuProps['items'] = [];
    let finalMenus = menus;
    let isPictureMessageCanAddAsEmoji = false;

    if (message.contentType === MessageType.PictureMessage) {
      const imageType = message?.pictureElem?.sourcePicture.type;
      if (
        imageType === 'image/jpeg' ||
        imageType === 'image/png' ||
        imageType === 'image/jpg' ||
        imageType === 'image/gif'
      ) {
        isPictureMessageCanAddAsEmoji = true;
      }
    }
    // 仅图片消息或表情消息，需要展示'添加到表情'
    if (
      message.contentType !== MessageType.FaceMessage &&
      !isPictureMessageCanAddAsEmoji
    ) {
      finalMenus = menus.filter((i) => i !== 'addAsEmoji');
    }

    items = finalMenus.map((key) => {
      if (
        message.contentType !== MessageType.TextMessage &&
        message.contentType !== MessageType.QuoteMessage &&
        message.contentType !== MessageType.AtTextMessage &&
        message.contentType !== MessageType.GroupAnnouncementUpdated &&
        // eslint-disable-next-line eqeqeq
        key == 'copy'
      ) {
        return;
      }
      if (message.contentType !== MessageType.MergeMessage && key === 'open') {
        return;
      }
      if (
        historyTab !== 'message' &&
        historyTab !== 'picture' &&
        !['forwardMsg', 'toSession', 'divider', 'remove'].includes(key)
      ) {
        return;
      }
      return menuButtons[key];
    });
    return items;
  }, [
    historyTab,
    menuButtons,
    message.contentType,
    message?.pictureElem?.sourcePicture.type,
  ]);

  const getSmartItems = useMemo(() => {
    let items: MenuProps['items'] = [];

    items = smartMenus.map((key) => {
      if (
        (message.contentType === MessageType.FileMessage ||
          message.contentType === MessageType.PictureMessage) &&
        // eslint-disable-next-line eqeqeq
        key == 'copy'
      ) {
        return;
      }
      if (message.contentType === MessageType.CustomMessage) {
        let content: any = {};
        try {
          content = JSON.parse(message?.customElem?.data || '{}');
        } catch (e) {
          content = message?.customElem?.data;
        }
        if (
          content?.type === 'stream' &&
          content?.content?.error &&
          key === 'copy'
        ) {
          return;
        }
      }

      if (
        (message.contentType === MessageType.TextMessage ||
          message.contentType === MessageType.CustomMessage) &&
        // eslint-disable-next-line eqeqeq
        key == 'downLoad'
      ) {
        return;
      }
      return smartMenuButtons[key];
    });
    return items;
  }, [message.contentType, message?.customElem?.data, smartMenuButtons]);

  const handleOpenMultiMessageView = useCallback(async () => {
    openMultiMessageWin(
      {
        message,
        conversationID,
        showName: currentConversation?.showName,
        hasForwardBtn: true,
      },
      () => {
        setMultiMessageModalOpen(true);
      }
    );
  }, [conversationID, message, currentConversation?.showName]);

  const handleOpenMultiMessageViewThrottle = throttle(
    handleOpenMultiMessageView,
    300,
    { leading: false, trailing: true }
  );

  async function copyImageFromUrl(toCopyUrl: string) {
    try {
      const response = await fetch(toCopyUrl);
      let blob = await response.blob();

      //  如果 blob.type 为空，默认设置为 image/png
      if (!blob.type) {
        blob = new Blob([blob], { type: 'image/png' });
      }

      await navigator.clipboard.write([
        new ClipboardItem({ [blob.type]: blob }),
      ]);

      feedbackToast({ msg: '复制成功' });
    } catch (err) {
      feedbackToast({ error: '复制失败' });
    }
  }

  const downLoad = async () => {
    if (message.contentType === MessageType.PictureMessage) {
      const { pictureElem } = message;
      const { url = '', uuid } = pictureElem?.sourcePicture || {};
      const fileName = uuid?.split(/[\\/]/).pop() || 'image.png';
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error('网络响应失败');
        }
        const blob = await response.blob();
        const blobUrl = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        URL.revokeObjectURL(blobUrl);
      } catch (error) {
        Message.error('下载失败');
      }
    } else if (message.contentType === MessageType.FileMessage) {
      const { fileElem } = message;
      const { fileName = '', sourceUrl = '' } = fileElem || {};
      const link = document.createElement('a');
      link.href = sourceUrl;
      link.download = fileName;
      link.click();
    }
  };

  const itemClick = ({ item, key }: any) => {
    switch (key) {
      case 'toSession':
        handleViewTarget();
        break;
      case 'copy':
        if (message.contentType === MessageType.PictureMessage) {
          copyImageFromUrl(message.pictureElem?.sourcePicture.url || '');
        } else {
          handleCopy(message, !selectedTextRef.current);
        }
        break;
      case 'open':
        handleOpenMultiMessageViewThrottle();
        break;
      case 'remove':
        tryRemove();
        break;
      case 'forwardMsg':
        setForwardModal(true);
        break;
      case 'addAsEmoji':
        addAsEmoji();
        break;
      case 'downLoad':
        downLoad();
        break;
      default:
        break;
    }
    setRightBtnOpen(false);
  };

  // 消息定位

  const isMultiSession = useConversationStore((state) => state.isMultiSession);
  const currentMultiSession = useConversationStore(
    (state) => state.currentMultiSession
  );

  const clearRightArea = useSmartAssitantStore((state) => state.clearRightArea);
  const handleViewTarget = async () => {
    const data = message;
    if (!data) {
      return;
    }
    if (data.contentType === MessageType.RevokeMessage) {
      return;
    }
    try {
      if (currentConversation?.conversationID != null) {
        if (isMultiSession && isInSmartAssitantantPage()) {
          if (isInSmartAssitantantHalfPage()) {
            clearRightArea();
          }
          updateChannelHeaderCurTabAndConversation(
            'message',
            {
              clientMsgID: data.clientMsgID,
              seq: data.seq,
            },
            {
              ...currentMultiSession,
              ex: `${uuidv4()}`,
            }
          );
        } else {
          updateChannelHeaderCurTabAndConversation(
            'message',
            {
              clientMsgID: data.clientMsgID,
              seq: data.seq,
            },
            {
              ...currentConversation,
              ex: `${uuidv4()}`,
            }
          );
        }
      }
    } catch (error) {
      updateTargetMsg({
        clientMsgID: data.clientMsgID,
        seq: undefined,
      });
    }
  };

  const tryRemove = async () => {
    try {
      await IMSDK.deleteMessage({
        clientMsgID: message.clientMsgID,
        conversationID,
      });
      deleteOneMessage(message.clientMsgID);
      deleteMultiSelect(message);
      if (message.groupID) {
        getCurrentMessageUpInfo(message.groupID);
      }
    } catch (error) {
      feedbackToast({ error, msg: '删除失败' });
    }
  };

  return (
    <>
      <Menu
        items={isInSmartAssitantantPage() ? getSmartItems : getItems}
        onClick={itemClick}
      />
      {forwardModal && (
        <ForwardModal
          open={forwardModal}
          onClose={() => setForwardModal(false)}
          message={message}
          isThread={false}
          isSender={false}
          conversationID={conversationID}
          showName={currentConversation?.showName}
        />
      )}
      {multiMessageModalOpen && (
        <MultiMessageModal
          modalOpen={multiMessageModalOpen}
          oncancel={() => setMultiMessageModalOpen(false)}
          hasForwardBtn={true}
          message={message}
          conversationID={conversationID}
          showName={currentConversation?.showName}
        />
      )}
    </>
  );
};
export default memo(HistoryRightButtonMenu);
