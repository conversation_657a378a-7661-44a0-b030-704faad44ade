import classNames from 'classnames';
import React, {
  memo,
  ReactNode,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from 'react';
import { isEmpty } from 'lodash';
import dayjs from 'dayjs';
import {
  CbEvents,
  ConversationItem,
  PublicUserItem,
  WSEvent,
} from '@ht/openim-wasm-client-sdk';
import { IMSDK } from '@/layouts/BasicLayout';
import { useUserStore } from '@/store';
import { UserStatusType } from '@/store/type';
import { Virtuoso } from '@ht/react-virtuoso';
import ChannelItem from './ChannelItem';
import styles from './index.less';

export interface UserDetailProps {
  status: UserStatusType;
  multiSession: number;
}
interface ChannleListItemProp {
  isActive: (prop: ConversationItem) => boolean;
  conversationIniting: boolean;
  allChatItems: ConversationItem[];
  // handleConversationClicked: (prop: any) => void;
  iconBoxRender?: (contact: ConversationItem) => ReactNode;
  extraItem?: (contact: ConversationItem) => ReactNode;
}

const ChannelItemList: React.FC<ChannleListItemProp> = ({
  isActive,
  conversationIniting,
  allChatItems,
  iconBoxRender = () => '',
  extraItem,
}) => {
  const [today, setToday] = useState(dayjs());
  const [userDetailMap, setUserDetailMap] = useState<
    Map<string, UserDetailProps>
  >(new Map());
  const { selfInfo, selfStatus } = useUserStore();

  // 已加载过的 userID，避免重复请求
  const loadedUserIDs = useRef(new Set<string>());

  // 当前可见区范围
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 0 });

  useEffect(() => {
    const timer = setInterval(() => {
      setToday(dayjs());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  const getUserStatusFromDetail = useCallback(
    (userIDParam: string, userDetailParam: PublicUserItem) => {
      if (userIDParam === selfInfo.userID) {
        return selfStatus;
      } else {
        return JSON.parse(userDetailParam?.ex || '{}')?.userState;
      }
    },
    [selfInfo.userID, selfStatus]
  );

  const getMultiSessionFromDetail = useCallback(
    (userIDParam: string, userDetailParam: PublicUserItem) => {
      if (userIDParam === selfInfo.userID) {
        return JSON.parse(selfInfo?.ex || '{}')?.multiSession;
      } else {
        return JSON.parse(userDetailParam?.ex || '{}')?.multiSession;
      }
    },
    [selfInfo?.ex, selfInfo.userID]
  );

  // 只获取当前可见区 + 未加载过的用户 ID
  useEffect(() => {
    const idsToQuery = allChatItems
      .slice(visibleRange.start, visibleRange.end + 1)
      .map((item) => item.userID)
      .filter(Boolean)
      .filter((id) => !loadedUserIDs.current.has(id));

    if (!isEmpty(idsToQuery)) {
      IMSDK.getUsersInfo(idsToQuery)
        .then((res) => {
          if (res?.data) {
            const newMap = new Map(userDetailMap);
            res.data.forEach((item) => {
              loadedUserIDs.current.add(item.userID);
              newMap.set(item.userID, {
                status: getUserStatusFromDetail(item.userID, item),
                multiSession: getMultiSessionFromDetail(item.userID, item),
              });
            });
            setUserDetailMap(newMap);
          }
        })
        .catch((e) => {
          console.error('查询用户信息报错', e);
        });
    }
  }, [
    visibleRange,
    allChatItems,
    getUserStatusFromDetail,
    getMultiSessionFromDetail,
    userDetailMap,
  ]);

  // 监听用户信息更新
  useEffect(() => {
    const updateUserDetail = ({ data }: WSEvent<PublicUserItem>) => {
      setUserDetailMap((prev) => {
        const newMap = new Map(prev);
        newMap.set(data.userID, {
          status: getUserStatusFromDetail(data.userID, data),
          multiSession: getMultiSessionFromDetail(data.userID, data),
        });
        return newMap;
      });
    };

    IMSDK.on(CbEvents.OnUserInfoUpdated, updateUserDetail);
    return () => {
      IMSDK.off(CbEvents.OnUserInfoUpdated, updateUserDetail);
    };
  }, [getMultiSessionFromDetail, getUserStatusFromDetail]);

  return (
    <div
      className={classNames(styles.channelItemListWrapper)}
      id="conversationList"
    >
      {!conversationIniting && (
        <Virtuoso
          data={allChatItems}
          increaseViewportBy={{ top: 1200, bottom: 1200 }}
          fixedItemHeight={67}
          rangeChanged={({ startIndex, endIndex }) => {
            setVisibleRange({ start: startIndex, end: endIndex });
          }}
          itemContent={(index, contact) => (
            <ChannelItem
              key={contact.conversationID}
              contact={contact}
              iconBoxRender={iconBoxRender(contact)}
              isLastPinnedItem={
                contact.isPinned && !allChatItems[index + 1]?.isPinned
              }
              isActive={isActive(contact)}
              userDetail={userDetailMap.get(contact.userID)}
              today={today}
            >
              {extraItem?.(contact)}
            </ChannelItem>
          )}
        />
      )}
    </div>
  );
};

export default memo(ChannelItemList);
