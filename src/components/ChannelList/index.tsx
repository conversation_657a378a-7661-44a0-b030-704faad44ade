import { useState, memo, useCallback, useMemo } from 'react';
import { useDeepCompareEffect } from 'ahooks';
import { useConversationStore } from '@/store';
import {
  ConversationItem,
  GroupItem,
  SessionType,
} from '@ht/openim-wasm-client-sdk';
import { useSearchParams } from '@/hooks/useNavigate';
import { feedbackToast } from '@/utils/common';
import { IMSDK } from '@/layouts/BasicLayout';
import emptyIcon from '@/assets/coversation/empty.svg';
import AddGroupModal from '../AddGroupModal';
import withScrollBar from '../withScrollBar';
import ChannelHeader from './ChannelHeader';
import OIMAvatar from '../OIMAvatar';
import ChannelItemList from './ChannelItemList';
import GroupAvatar from './GroupAvatar';
// import ThreadItem from './ThreadItem';
import styles from './index.less';

const ChannelList = () => {
  const [showGroupCardModal, setShowGroupCardModal] = useState(false);

  const [searchParams, setSearchParams] = useSearchParams();

  const paramsConversationId = searchParams.get('conversationId');

  const conversationIniting = useConversationStore(
    (state) => state.conversationIniting
  );

  const conversationList = useConversationStore(
    (state) => state.conversationList
  );
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );

  const channelHeaderCurTab = useConversationStore(
    (state) => state.channelHeaderCurTab
  );

  const updateCurrentConversation = useConversationStore(
    (state) => state.updateCurrentConversation
  );

  // const updateConversation = useConversationStore(
  //   (state) => state.updateConversation
  // );

  const updateChannelHeaderCurTab = useConversationStore(
    (state) => state.updateChannelHeaderCurTab
  );

  const updateCurrentMemberInGroupIniting = useConversationStore(
    (state) => state.updateCurrentMemberInGroupIniting
  );

  // const updateReadSeqInfo = useConversationStore(
  //   (state) => state.updateReadSeqInfo
  // );
  // const currentMessageInputValue = useConversationStore(
  //   (state) => state.currentMessageInputValue
  // );

  const conversationID = useMemo(() => {
    return currentConversation?.conversationID || '';
  }, [currentConversation?.conversationID]);

  const allChatItems = useMemo(() => {
    return (
      conversationList?.filter(
        (conversation) =>
          conversation?.parentId == null || conversation?.parentId === ''
      ) ?? []
    );
  }, [conversationList]);

  useDeepCompareEffect(() => {
    if (!conversationIniting) {
      if (paramsConversationId != null) {
        const paramsConversation = conversationList?.filter(
          (item) => item.conversationID === paramsConversationId
        )?.[0];
        if (paramsConversation != null) {
          setSearchParams(new URLSearchParams({})); // 跳转后清理，否则的话回
          updateCurrentConversation(paramsConversation);
          return;
        } else {
          feedbackToast({
            error: '跳转会话失败',
            msg: '跳转会话失败，会话ID不存在',
          });
        }
        setSearchParams(new URLSearchParams({})); // 跳转后清理，否则的话回
      }

      // 默认选中第一个会话
      if (allChatItems.length > 0 && currentConversation == null) {
        updateCurrentMemberInGroupIniting(true);
        updateCurrentConversation(allChatItems[0]);
      }
    }
  }, [
    conversationIniting,
    paramsConversationId,
    allChatItems,
    conversationList,
    currentConversation,
    updateCurrentConversation,
  ]);

  const getThreadsbyGroupID = useCallback(
    (groupID: string) => {
      return (
        conversationList?.filter(
          (conversation) =>
            conversation.groupID && conversation?.parentId === groupID
        ) ?? []
      );
    },
    [conversationList]
  );

  const isActiveGroup = useCallback(
    (groupID: string) => {
      if (currentConversation?.groupID === groupID) {
        return true;
      }
      return false;
    },
    [currentConversation?.groupID]
  );

  const jumpToNewGroup = (newGroup: GroupItem) => {
    setTimeout(async () => {
      const conversation = await IMSDK.getOneConversation({
        sessionType: SessionType.Group,
        sourceID: newGroup.groupID,
      });

      if (channelHeaderCurTab !== 'message') {
        updateChannelHeaderCurTab('message');
      }
      updateCurrentConversation({
        ...conversation.data,
      });
    }, 100);
  };

  return (
    <div className={styles.channelListWrapper}>
      <ChannelHeader handleAddClick={() => setShowGroupCardModal(true)} />
      <div className={styles.listarea}>
        {!conversationIniting && conversationList?.length === 0 && (
          <div className={styles.listEmptyDesc}>
            <img src={emptyIcon} />
            <span>无相关会话记录</span>
          </div>
        )}
        <ChannelItemList
          isActive={(contact: ConversationItem) =>
            !contact.groupID
              ? conversationID === contact.conversationID ||
                conversationID === contact.parentId
              : isActiveGroup(contact.groupID)
          }
          allChatItems={allChatItems}
          conversationIniting={conversationIniting}
          // handleConversationClicked={handleConversationClicked}
          iconBoxRender={(contact: ConversationItem) =>
            contact.groupID ? (
              <GroupAvatar faceMember={contact?.faceMember} size={'small'} />
            ) : (
              <OIMAvatar
                userID={contact?.userID}
                size={42}
                stateSize={11}
                stateRight={-2}
                hideOnlineStatus={true}
              />
            )
          }
        />
      </div>
      {showGroupCardModal && (
        <AddGroupModal
          visible={showGroupCardModal}
          handleCancel={() => {
            setShowGroupCardModal(false);
          }}
          afterConfirmBtnClick={jumpToNewGroup}
          type={'create'}
        />
      )}
    </div>
  );
};

export default memo(
  withScrollBar(ChannelList, {
    domId: 'channel-drag-scroll-bar-left',
    defaultWidth: '378px',
    minWidth: 294,
    maxWidth: 930,
    dragBarClassName: 'scrollbarLeft',
  })
);
