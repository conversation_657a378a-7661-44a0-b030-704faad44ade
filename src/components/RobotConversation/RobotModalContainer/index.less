.robotAnswerSource {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--primary-background-color-17);

  .answerSourceHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 16px 24px;

    .answerSourceTitle {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      line-height: 28px;
      color: var(--primary-text-color-1);

      > img {
        width: 28px;
        height: 28px;
        margin-right: 4px;
      }
    }
    .closeIcon {
      width: 28px;
      height: 28px;
      cursor: pointer;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .line {
    display: none;
  }
  .warp {
    flex: 1 1;
    padding: 8px 20px 16px 0;
    overflow-y: auto;
    .item {
      padding: 12px;
      background: var(--primary-background-color-6);
      border-radius: 8px;
      margin-bottom: 8px;
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 6px;
        .sourceName {
          flex: 1 1;
          display: flex;
          align-items: center;
          font-size: 15px;
          font-weight: 600;
          color: var(--primary-text-color-1);
          line-height: 22px;
          overflow: hidden;
          > img {
            width: 24px;
            height: 24px;
            margin-right: 8px;
          }
          > span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .docName:hover {
            // text-decoration: underline;
          }
        }
        .serialNumber {
          height: 17px;
          font-size: 12px;
          font-weight: 400;
          color: var(--primary-text-color-1);
          line-height: 17px;
          padding: 0 5px;
          border-radius: 12px;
          background-color: color-mix(
            in srgb,
            var(--tab-actived-background-color),
            transparent 70%
          );
          margin-left: 10px;
        }
      }
      .content {
        font-size: 13px;
        font-weight: 400;
        color: var(--primary-text-color-3);
        line-height: 20px;
        word-break: break-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3; /* 控制显示的行数 */
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 4px;
        cursor: pointer;
      }
      .url {
        font-size: 13px;
        font-weight: 400;
        color: var(--primary-text-color-3);
        line-height: 18px;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      &:hover {
        background: var(--primary-background-color-15);
        .docName {
          // color: var(--primary-text-color-9);
        }
      }
    }
    .active {
      background: var(--tab-actived-background-color);
    }
  }

  &.inSmart {
    background-color: #fff;

    .answerSourceHeader {
      padding: 16px 23px 16px 24px;

      .answerSourceTitle {
        font-size: 16px;
        height: 24px;
        line-height: 24px;
        color: #000000;
      }

      .closeIcon {
        width: 24px;
        height: 24px;
      }
    }
    .line {
      display: block;
      width: 100%;
      height: 1px;
      background: #f2f2f3;
      margin-bottom: 20px;
    }
    .warp {
      padding: 8px 15px 0 16px;
    }
  }
}
