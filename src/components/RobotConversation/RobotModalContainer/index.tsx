import { FC } from 'react';
import onlineSearchIcon from '@/assets/channel/robotConversationList/onlineSearch.svg';
import closeIcon from '@/assets/channel/robotConversationList/close.svg';
import closeIconInSmart from '@/assets/closeIcon.svg';
import RightAreaBackBtn from '@/components/RightAreaBackBtn';
import classNames from 'classnames';
import { isInSmartAssitantantPage } from '@/utils/utils';
import styles from './index.less';

interface RobotModalContainerProps {
  title: string;
  onClose: () => void;
}

const RobotModalContainer: FC<RobotModalContainerProps> = ({
  title,
  onClose,
  children,
}) => {
  return (
    <div
      className={classNames(
        styles.robotAnswerSource,
        isInSmartAssitantantPage() && styles.inSmart
      )}
    >
      <div className={styles.answerSourceHeader}>
        <div className={styles.answerSourceTitle}>
          <RightAreaBackBtn />
          {!isInSmartAssitantantPage() && <img src={onlineSearchIcon} />}
          <span>{title}</span>
        </div>
        <div className={styles.closeIcon}>
          <img
            src={isInSmartAssitantantPage() ? closeIconInSmart : closeIcon}
            onClick={onClose}
          />
        </div>
      </div>
      <div className={styles.line}></div>
      <div className={styles.warp}>{children}</div>
    </div>
  );
};
export default RobotModalContainer;
