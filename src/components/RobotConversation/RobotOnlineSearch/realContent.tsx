import { FC, useEffect, useState } from 'react';
import classNames from 'classnames';
import styles from '../RobotAnswerSource/index.less';

interface sourceItem {
  index: number;
  title: string;
  url: string;
  content: string;
}

interface RobotOnlineSearchRealContentProps {
  data: sourceItem[];
  activeValue?: sourceItem;
}

const RobotOnlineSearchRealContent: FC<RobotOnlineSearchRealContentProps> = ({
  data = [],
  activeValue,
}) => {
  const [selectObj, setSelectObj] = useState<sourceItem | undefined>(undefined);

  useEffect(() => {
    setSelectObj(activeValue || undefined);
  }, [activeValue]);

  const openNewWindow = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <div style={{ marginTop: '-8px' }}>
      {data.map((item: sourceItem) => {
        return (
          <div
            className={classNames(
              styles.item,
              selectObj && selectObj.index === item.index && styles.active
            )}
            key={item.index}
            onClick={() => openNewWindow(item.url)}
          >
            <div className={styles.header}>
              <div className={styles.sourceName}>
                <span>{item.title}</span>
              </div>
              <div className={styles.serialNumber}>{item.index}</div>
            </div>
            <div className={styles.content}>{item.content}</div>
            <div className={styles.url}>{item.url}</div>
          </div>
        );
      })}
    </div>
  );
};

export default RobotOnlineSearchRealContent;
