import { FC } from 'react';
import RobotModalContainer from '../RobotModalContainer';
import RealContent from './realContent';

interface sourceItem {
  index: number;
  title: string;
  url: string;
  content: string;
}

interface RobotOnlineSearchProps {
  onClose: () => void;
  data: sourceItem[];
  activeValue?: sourceItem;
  title?: string;
}

const RobotOnlineSearch: FC<RobotOnlineSearchProps> = ({
  onClose,
  data = [],
  activeValue,
  title = '联网搜索',
}) => {
  return (
    <RobotModalContainer title={title} onClose={onClose}>
      <RealContent data={data} activeValue={activeValue} />
    </RobotModalContainer>
  );
};
export default RobotOnlineSearch;
