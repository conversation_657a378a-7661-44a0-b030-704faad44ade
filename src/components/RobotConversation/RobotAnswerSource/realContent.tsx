import { FC, useEffect, useState } from 'react';
import {
  isInSmartAssitantantPage,
  isInSmartAssitantantHalfPage,
  isInSmartAssitantantFullPage,
  getDocIcon,
  docTypeEnum,
} from '@/utils/utils';
import classNames from 'classnames';
import { processImageUrls } from '@/components/Channel/components/MessageItem/StreamMessage';
import { useSmartAssitantStore } from '@/store';
import pictureIcon from '@/assets/channel/robotConversationList/answerSource/picture.svg';
import xmlIcon from '@/assets/channel/robotConversationList/answerSource/xml.svg';
import htmlIcon from '@/assets/channel/robotConversationList/answerSource/html.svg';
import txtIcon from '@/assets/channel/robotConversationList/answerSource/txt.svg';
import otherIcon from '@/assets/channel/robotConversationList/answerSource/other.svg';

import { canSendImageTypeList } from '@/utils/common';
import styles from './index.less';

import RobotAnswerSourceModal from './modal';

export const getFileIcon = (fileName: string) => {
  try {
    const idx = fileName.lastIndexOf('.');
    const fileType = fileName.slice(idx + 1).toLowerCase();
    const isImage = canSendImageTypeList.includes(fileType);
    if (isImage) {
      return pictureIcon;
    }
    switch (fileType) {
      case 'doc':
        return getDocIcon(docTypeEnum.WORD);
      case 'docx':
        return getDocIcon(docTypeEnum.WORD);
      case 'xls':
        return getDocIcon(docTypeEnum.EXCEL);
      case 'xlsx':
        return getDocIcon(docTypeEnum.EXCEL);
      case 'ppt':
        return getDocIcon(docTypeEnum.PPT);
      case 'pptx':
        return getDocIcon(docTypeEnum.PPT);
      case 'pdf':
        return getDocIcon(docTypeEnum.PDF);
      case 'xmind':
        return getDocIcon(docTypeEnum.MINDMAPNEW);
      case 'md':
        return getDocIcon(docTypeEnum.MARKDOWN);
      case 'txt':
        return txtIcon;
      case 'xml':
        return xmlIcon;
      case 'html':
        return htmlIcon;
      default:
        return otherIcon;
    }
  } catch (error) {
    return otherIcon;
  }
};

export interface sourceItem {
  index: number;
  score: number;
  doc_name: string;
  doc_type: number;
  doc_url: string;
  content: string;
}

interface RobotAnswerSourceProps {
  data: sourceItem[];
  activeValue?: sourceItem;
}

const RobotAnswerSourceRealContent: FC<RobotAnswerSourceProps> = ({
  data = [],
  activeValue,
}) => {
  const setSelectObjInSmart = useSmartAssitantStore(
    (state) => state.setSelectObj
  );
  const [open, setOpen] = useState<boolean>(false);
  const [selectObj, setSelectObj] = useState<sourceItem | undefined>(undefined);

  useEffect(() => {
    if (!isInSmartAssitantantPage()) {
      setSelectObj(activeValue || undefined);
    }
  }, [activeValue]);

  const openNewWindow = (url: string) => {
    url && window.open(url, '_blank');
  };

  const openModal = (obj: sourceItem) => {
    if (isInSmartAssitantantPage()) {
      setSelectObjInSmart(obj);
    } else {
      setSelectObj(obj);
      setOpen(true);
    }
  };

  const handleCancel = () => {
    if (isInSmartAssitantantPage()) {
      setSelectObjInSmart(undefined);
    } else {
      setSelectObj(undefined);
      setOpen(false);
    }
  };

  return (
    <div style={{ marginTop: '-8px' }}>
      {data.map((item: sourceItem) => {
        return (
          <div
            className={classNames(
              styles.item,
              isInSmartAssitantantHalfPage() && styles.inHalfSmart,
              isInSmartAssitantantFullPage() && styles.inFullSmart,
              selectObj && selectObj.index === item.index && styles.active
            )}
            key={item.index}
          >
            <div className={styles.header}>
              <div className={styles.sourceName}>
                <img src={getFileIcon(item.doc_name)} />
                <span
                  className={styles.docName}
                  onClick={() => openNewWindow(item.doc_url)}
                >
                  {item.doc_name}
                </span>
              </div>
              <div className={styles.serialNumber}>{item.index}</div>
            </div>
            <div className={styles.content} onClick={() => openModal(item)}>
              {processImageUrls(item.content)}
            </div>
          </div>
        );
      })}

      {!isInSmartAssitantantPage() && (
        <RobotAnswerSourceModal
          open={open}
          handleCancel={handleCancel}
          selectObj={selectObj}
        />
      )}
    </div>
  );
};
export default RobotAnswerSourceRealContent;
