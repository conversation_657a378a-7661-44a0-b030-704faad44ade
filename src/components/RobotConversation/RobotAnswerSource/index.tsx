import { FC } from 'react';
import RealContent from './realContent';
import RobotModalContainer from '../RobotModalContainer';

interface sourceItem {
  index: number;
  score: number;
  doc_name: string;
  doc_type: number;
  doc_url: string;
  content: string;
}

interface RobotAnswerSourceProps {
  onClose: () => void;
  data: sourceItem[];
  activeValue?: sourceItem;
}

const RobotAnswerSource: FC<RobotAnswerSourceProps> = ({
  onClose,
  data = [],
  activeValue,
}) => {
  return (
    <RobotModalContainer title="回答来源" onClose={onClose}>
      <RealContent data={data} activeValue={activeValue} />
    </RobotModalContainer>
  );
};
export default RobotAnswerSource;
