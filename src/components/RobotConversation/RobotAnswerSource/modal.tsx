import closeIcon from '@/assets/channel/robotConversationList/close.svg';
import { Modal } from '@ht/sprite-ui';
import { RenderMd } from '@/components/MdEditor';
import { processImageUrls } from '@/components/Channel/components/MessageItem/StreamMessage';
import styles from './index.less';
import { getFileIcon, sourceItem } from './realContent';

interface RobotAnswerSourceModalProps {
  open: boolean;
  handleCancel: () => void;
  selectObj?: sourceItem;
}

const RobotAnswerSourceModal: React.FC<RobotAnswerSourceModalProps> = ({
  open,
  handleCancel,
  selectObj,
}) => {
  return (
    <Modal
      open={open}
      onCancel={() => handleCancel()} // 取消按钮回调
      footer={null}
      centered={true}
      closable={false}
      destroyOnClose={true}
      width={720}
      className={styles.answerSourceModal}
    >
      <div className={styles.answerSourceModalWarp}>
        <div className={styles.header}>
          <div>回答来源</div>
          <img src={closeIcon} onClick={handleCancel} />
        </div>
        <div className={styles.content}>
          <div className={styles.docName}>
            <img src={getFileIcon(selectObj?.doc_name || '')} />
            <span>{selectObj?.doc_name}</span>
          </div>
          <div className={styles.box}>
            <RenderMd
              id={`${selectObj?.score || ''}_RobotAnswerSourceModal`}
              value={processImageUrls(selectObj?.content || '')}
            />
          </div>
        </div>
      </div>
    </Modal>
  );
};
export default RobotAnswerSourceModal;
