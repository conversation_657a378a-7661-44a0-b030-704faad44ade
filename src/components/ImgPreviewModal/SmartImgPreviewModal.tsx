import { Modal, Tooltip } from '@ht/sprite-ui';
import closeModalIcon from '@/assets/smartAssistant/closeModalIcon.svg';
import smartAddIcon from '@/assets/smartAssistant/imgMsg/addIcon.svg';
import smartCopyIcon from '@/assets/smartAssistant/imgMsg/copyIcon.svg';
import smartDownLoadIcon from '@/assets/smartAssistant/imgMsg/downLoadIcon.svg';
import smartReduceIcon from '@/assets/smartAssistant/imgMsg/reduceIcon.svg';
import { feedbackToast } from '@/utils/common';
import styles from './SmartImgPreviewModal.less';

interface SmartImgPreviewModalProps {
  open: any;
  onClose: () => void;
  previewUrl?: string;
  type?: string;
  width: number;
  height: number;
  imgRef: any;
  url: string;
  reset: () => void;
  scaleNumber: number;
  rotateNum: number;
  move: { left: number; top: number };
  download?: () => void;
  reduce: () => void;
  add: () => void;
}
const SmartImgPreviewModal: React.FC<SmartImgPreviewModalProps> = ({
  open,
  onClose,
  previewUrl,
  width,
  height,
  imgRef,
  url,
  reset,
  scaleNumber,
  rotateNum,
  move,
  download,
  reduce,
  add,
}) => {
  // 需要https权限，暂时隐藏
  // async function copyImageFromUrl(toCopyUrl: string) {
  //   try {
  //     const response = await fetch(toCopyUrl);
  //     let blob = await response.blob();

  //     //  如果 blob.type 为空，默认设置为 image/png
  //     if (!blob.type) {
  //       blob = new Blob([blob], { type: 'image/png' });
  //     }

  //     await navigator.clipboard.write([
  //       new ClipboardItem({ [blob.type]: blob }),
  //     ]);

  //     feedbackToast({ msg: '复制成功' });
  //   } catch (err) {
  //     feedbackToast({ error: '复制失败' });
  //   }
  // }

  return (
    <Modal
      title={null}
      footer={null}
      closable={false}
      centered={true}
      open={open}
      onCancel={onClose}
      wrapClassName={styles.smartPreviewModal}
      width={'100vw'}
      maskStyle={{ backgroundColor: 'transparent' }}
      bodyStyle={{
        padding: 0,
      }}
      style={{
        overflow: 'hidden',
        maxWidth: '100vw',
        // opacity: 0.1,
      }}
      maskClosable={false}
      keyboard={true}
    >
      <div className={styles.imgPreviewModalWarp}>
        <div className={styles.header}>
          <div>
            <div className={styles.closeIcon} onClick={onClose}>
              <img src={closeModalIcon} />
            </div>
          </div>
        </div>
        <div className={styles.warp}>
          <div
            className={styles.imgBox}
            style={{
              // 当有明确的width和height时使用，否则让容器自适应
              ...(width && height ? { width, height } : {}),
              maxHeight: '100%',
              maxWidth: '100%',
            }}
          >
            <img
              ref={imgRef}
              src={url}
              onDoubleClick={reset}
              style={{
                transform: `scale(${scaleNumber}) rotate(${rotateNum}deg)`,
                left: move.left,
                top: move.top,
              }}
              draggable={false}
            />
          </div>
        </div>
        <div className={styles.footer}>
          <div className={styles.left}>
            <Tooltip title="缩小">
              <div className={styles.item} onClick={reduce}>
                <img src={smartReduceIcon} />
              </div>
            </Tooltip>
            <Tooltip title="放大">
              <div className={styles.item} onClick={add}>
                <img src={smartAddIcon} />
              </div>
            </Tooltip>
            <div className={styles.line}></div>
            {/* <Tooltip title="复制">
              <div
                className={styles.item}
                onClick={() => copyImageFromUrl(previewUrl!)}
              >
                <img src={smartCopyIcon} />
              </div>
            </Tooltip> */}
            <Tooltip title="下载">
              <div className={styles.item} onClick={download}>
                <img src={smartDownLoadIcon} />
              </div>
            </Tooltip>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default SmartImgPreviewModal;
