.imgPreviewModalWarp {
  width: 100%;
  height: 100vh;
  padding: 148px 0;
  background: rgba(0, 0, 0, 50%);
  position: relative;
  overflow: hidden;
  .header {
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    width: 100%;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: right;

    > div {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px;
      position: relative;
      z-index: 1;
    }
    .closeIcon {
      width: 40px;
      height: 40px;
      border-radius: 100%;
      margin-right: 24px;
      background: #ffffff;
      border: 1px solid #d7d7d7;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 99;
      cursor: pointer;

      img {
        width: 24px;
        height: 24px;
      }
    }
  }
  .warp {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .imgBox {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      // 当没有固定尺寸时，确保容器能够自适应内容
      min-width: 0;
      min-height: 0;
      // 确保容器在父级中居中
      margin: auto;
      > img {
        cursor: grab;
        user-select: none;
        max-width: 100%;
        max-height: 100%;
        // 确保图片在容器中居中显示
        display: block;
        &[style*="position:absolute"] {
          position: absolute;
        }
      }
    }
  }
  .footer {
    display: flex;
    flex-direction: column-reverse;
    position: absolute;
    bottom: 48px;
    left: calc(50% - 100px);
    width: 200px;
    height: 52px;
    background: #ffffff;
    border-radius: 26px;
    border: 1px solid #d7d7d7;
    > div {
      display: flex;
      align-items: center;
      padding: 12px;
      justify-content: space-between;
    }
    .left {
      display: flex;
      align-items: center;
      justify-content: center;

      > :not(:nth-child(1)) {
        margin-left: 16px;
      }
      .item {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .line {
        height: 16px;
        background: #d8d8d8;
        width: 1px;
      }
    }
    .right {
      > div {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
      > div:hover {
        background-color: var(--link-color-base-inv-pry);
      }
    }
  }
}

.progressComponentWarp {
  display: flex;
  align-items: center;
  height: 32px;
  border-radius: 4px;
  border: 1px solid var(--primary-text-color-2);
  margin: 0 10px;
  padding: 0 8px;
  .btn {
    display: flex;
    align-items: center;
    cursor: pointer;
    > img {
      user-select: none;
    }
  }
  .line {
    width: 62px;
    height: 100%;
    margin: 0 8px;
  }
}

.silderWarp {
  :global {
    .linkflow-slider-rail {
      height: 3px;
    }
    .linkflow-slider-track {
      height: 3px;
      background: var(--primary-text-color-pressed);
    }
    .linkflow-slider-handle {
      width: 14px;
      height: 14px;
      background: var(--file-backgroud-color-1);
      margin-top: -6px;
    }
  }
}

.smartPreviewModal {
  &::before {
    display: none;
  }
  :global {
    .linkflow-modal-content,
    .linkflow-modal-wrap {
      background-color: transparent;
    }
  }
}
