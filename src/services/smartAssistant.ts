import request from '@/utils/request';

export interface StyleType {
  agentServiceStyle: {
    style: string;
  };
  audioMode: {
    enable: boolean;
  };
}
// 设置智能助理的答复偏好
export async function setResStyles(params: StyleType) {
  return request('/linq/bff/api/preferenceConfig/create', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: {
      'content-Type': 'application/json',
    },
  });
}

// 查询智能助理的答复偏好
export async function getResStyles(): Promise<StyleType> {
  return request('/linq/bff/api/preferenceConfig/get', {
    method: 'get',
  });
}

// 更新智能助理的偏好
export async function updateResStyles(params: StyleType) {
  return request('/linq/bff/api/preferenceConfig/update', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: {
      'content-Type': 'application/json',
    },
  });
}
