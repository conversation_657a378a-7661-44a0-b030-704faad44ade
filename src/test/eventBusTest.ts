/**
 * IM外部交互事件总线功能测试
 * 
 * 本文件用于测试事件总线的基本功能，确保重构后的事件系统正常工作
 */

import emitter, { emit, ExternalInteractionEvents } from '@/utils/events';

// 测试事件监听器
const testEventListeners = () => {
  console.log('开始测试事件总线功能...');

  // 测试计数器
  let testResults = {
    inputContainerResize: false,
    smartInputChanged: false,
    smartPageClicked: false,
    smartPageLoaded: false,
  };

  // 1. 测试输入容器尺寸变化事件
  const onInputContainerResize = (data: { height: number }) => {
    console.log('✅ EXTERNAL_INPUT_CONTAINER_RESIZE 事件接收成功:', data);
    testResults.inputContainerResize = data.height === 100;
  };

  // 2. 测试智能输入内容变化事件
  const onSmartInputChanged = (data: { content: string }) => {
    console.log('✅ EXTERNAL_SMART_INPUT_CHANGED 事件接收成功:', data);
    testResults.smartInputChanged = data.content === 'test content';
  };

  // 3. 测试智能助手页面点击事件
  const onSmartPageClicked = () => {
    console.log('✅ EXTERNAL_SMART_PAGE_CLICKED 事件接收成功');
    testResults.smartPageClicked = true;
  };

  // 4. 测试智能助手页面加载完成事件
  const onSmartPageLoaded = () => {
    console.log('✅ EXTERNAL_SMART_PAGE_LOADED 事件接收成功');
    testResults.smartPageLoaded = true;
  };

  // 订阅事件
  emitter.on('EXTERNAL_INPUT_CONTAINER_RESIZE', onInputContainerResize);
  emitter.on('EXTERNAL_SMART_INPUT_CHANGED', onSmartInputChanged);
  emitter.on('EXTERNAL_SMART_PAGE_CLICKED', onSmartPageClicked);
  emitter.on('EXTERNAL_SMART_PAGE_LOADED', onSmartPageLoaded);

  // 发送测试事件
  setTimeout(() => {
    console.log('发送测试事件...');
    
    emit('EXTERNAL_INPUT_CONTAINER_RESIZE', { height: 100 });
    emit('EXTERNAL_SMART_INPUT_CHANGED', { content: 'test content' });
    emit('EXTERNAL_SMART_PAGE_CLICKED');
    emit('EXTERNAL_SMART_PAGE_LOADED');

    // 检查测试结果
    setTimeout(() => {
      console.log('\n测试结果:');
      console.log('EXTERNAL_INPUT_CONTAINER_RESIZE:', testResults.inputContainerResize ? '✅ 通过' : '❌ 失败');
      console.log('EXTERNAL_SMART_INPUT_CHANGED:', testResults.smartInputChanged ? '✅ 通过' : '❌ 失败');
      console.log('EXTERNAL_SMART_PAGE_CLICKED:', testResults.smartPageClicked ? '✅ 通过' : '❌ 失败');
      console.log('EXTERNAL_SMART_PAGE_LOADED:', testResults.smartPageLoaded ? '✅ 通过' : '❌ 失败');

      const allPassed = Object.values(testResults).every(result => result);
      console.log('\n总体结果:', allPassed ? '✅ 所有测试通过' : '❌ 部分测试失败');

      // 清理事件监听器
      emitter.off('EXTERNAL_INPUT_CONTAINER_RESIZE', onInputContainerResize);
      emitter.off('EXTERNAL_SMART_INPUT_CHANGED', onSmartInputChanged);
      emitter.off('EXTERNAL_SMART_PAGE_CLICKED', onSmartPageClicked);
      emitter.off('EXTERNAL_SMART_PAGE_LOADED', onSmartPageLoaded);

      console.log('事件监听器已清理');
    }, 100);
  }, 100);
};

// 测试类型安全性
const testTypeSafety = () => {
  console.log('\n测试TypeScript类型安全性...');

  // 正确的事件发送（应该编译通过）
  emit('EXTERNAL_INPUT_CONTAINER_RESIZE', { height: 200 });
  emit('EXTERNAL_SMART_INPUT_CHANGED', { content: 'hello' });
  emit('EXTERNAL_SMART_PAGE_CLICKED');
  emit('EXTERNAL_SMART_PAGE_LOADED');

  // 错误的事件发送（应该编译报错，但在运行时测试中注释掉）
  // emit('EXTERNAL_INPUT_CONTAINER_RESIZE', { width: 200 }); // 错误：应该是height
  // emit('EXTERNAL_SMART_INPUT_CHANGED', { text: 'hello' }); // 错误：应该是content
  // emit('EXTERNAL_SMART_PAGE_CLICKED', { data: 'test' }); // 错误：不应该有参数

  console.log('✅ 类型安全性测试通过（编译时检查）');
};

// 测试事件取消订阅
const testEventUnsubscribe = () => {
  console.log('\n测试事件取消订阅...');

  let eventReceived = false;
  const testHandler = () => {
    eventReceived = true;
  };

  // 订阅事件
  emitter.on('EXTERNAL_SMART_PAGE_CLICKED', testHandler);

  // 发送事件（应该接收到）
  emit('EXTERNAL_SMART_PAGE_CLICKED');

  setTimeout(() => {
    if (eventReceived) {
      console.log('✅ 事件订阅正常工作');
      
      // 取消订阅
      emitter.off('EXTERNAL_SMART_PAGE_CLICKED', testHandler);
      eventReceived = false;

      // 再次发送事件（不应该接收到）
      emit('EXTERNAL_SMART_PAGE_CLICKED');

      setTimeout(() => {
        if (!eventReceived) {
          console.log('✅ 事件取消订阅正常工作');
        } else {
          console.log('❌ 事件取消订阅失败');
        }
      }, 50);
    } else {
      console.log('❌ 事件订阅失败');
    }
  }, 50);
};

// 导出测试函数
export const runEventBusTests = () => {
  console.log('=== IM外部交互事件总线功能测试 ===\n');
  
  testEventListeners();
  testTypeSafety();
  
  setTimeout(() => {
    testEventUnsubscribe();
  }, 500);
};

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，可以通过控制台调用测试
  (window as any).runEventBusTests = runEventBusTests;
  console.log('事件总线测试函数已挂载到 window.runEventBusTests，可在控制台调用');
}

export default {
  runEventBusTests,
  testEventListeners,
  testTypeSafety,
  testEventUnsubscribe,
};
