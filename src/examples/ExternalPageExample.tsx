/**
 * 外部页面集成示例
 * 
 * 本文件展示了如何在外部页面中集成IM智能助手，并处理各种外部交互事件
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import RenderParcel from '@/pages/smartAssistantHalfPage/RenderParcel';

interface ExternalPageExampleProps {
  // 外部页面的其他props
  title?: string;
  onClose?: () => void;
}

const ExternalPageExample: React.FC<ExternalPageExampleProps> = ({
  title = '智能助手集成示例',
  onClose,
}) => {
  // 状态管理
  const [containerHeight, setContainerHeight] = useState<number>(300);
  const [currentInput, setCurrentInput] = useState<string>('');
  const [isPageReady, setIsPageReady] = useState<boolean>(false);
  const [clickCount, setClickCount] = useState<number>(0);
  const [logs, setLogs] = useState<string[]>([]);

  // 引用
  const containerRef = useRef<HTMLDivElement>(null);

  // 添加日志的辅助函数
  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev.slice(-9), `[${timestamp}] ${message}`]);
  }, []);

  // 事件处理函数

  /**
   * 处理输入容器尺寸变化
   * 根据IM输入框的高度调整外部页面布局
   */
  const handleInputContainerResize = useCallback((data: { height: number }) => {
    console.log('输入容器高度变化:', data.height);
    setContainerHeight(data.height);
    addLog(`输入容器高度变化: ${data.height}px`);

    // 根据高度调整外部容器样式
    if (containerRef.current) {
      containerRef.current.style.paddingBottom = `${data.height + 20}px`;
    }
  }, [addLog]);

  /**
   * 处理智能输入内容变化
   * 可以根据输入内容提供搜索建议或其他交互功能
   */
  const handleSmartInputChanged = useCallback((content: string) => {
    console.log('智能输入内容变化:', content);
    setCurrentInput(content);
    addLog(`输入内容变化: "${content}"`);

    // 示例：根据输入内容提供搜索建议
    if (content.length > 2) {
      // 这里可以调用搜索API或显示建议
      addLog(`触发搜索建议: "${content}"`);
    }
  }, [addLog]);

  /**
   * 处理智能助手页面点击
   * 可以用于统计用户交互或触发其他UI变化
   */
  const handleSmartPageClicked = useCallback(() => {
    console.log('智能助手页面被点击');
    setClickCount(prev => prev + 1);
    addLog('智能助手页面被点击');

    // 示例：点击时改变外部页面样式
    if (containerRef.current) {
      containerRef.current.style.borderColor = '#1890ff';
      setTimeout(() => {
        if (containerRef.current) {
          containerRef.current.style.borderColor = '#d9d9d9';
        }
      }, 200);
    }
  }, [addLog]);

  /**
   * 处理智能助手页面加载完成
   * 页面准备就绪后可以进行初始化操作
   */
  const handleSmartPageLoaded = useCallback(() => {
    console.log('智能助手页面加载完成');
    setIsPageReady(true);
    addLog('智能助手页面加载完成');

    // 示例：页面加载完成后的初始化操作
    // 可以发送初始化数据或设置默认状态
  }, [addLog]);

  /**
   * setHost函数 - 用于设置主机相关配置
   * 具体用途需要根据实际业务需求确定
   */
  const setHost = useCallback((host: any) => {
    console.log('设置主机:', host);
    addLog(`设置主机: ${JSON.stringify(host)}`);
  }, [addLog]);

  // 清理日志
  const clearLogs = useCallback(() => {
    setLogs([]);
  }, []);

  // 组件样式
  const containerStyle: React.CSSProperties = {
    display: 'flex',
    height: '100vh',
    fontFamily: 'Arial, sans-serif',
  };

  const sidebarStyle: React.CSSProperties = {
    width: '300px',
    padding: '20px',
    backgroundColor: '#f5f5f5',
    borderRight: '1px solid #d9d9d9',
    overflow: 'auto',
  };

  const mainStyle: React.CSSProperties = {
    flex: 1,
    position: 'relative',
    border: '2px solid #d9d9d9',
    borderRadius: '8px',
    margin: '10px',
    transition: 'border-color 0.2s ease',
  };

  const statusStyle: React.CSSProperties = {
    marginBottom: '20px',
    padding: '15px',
    backgroundColor: 'white',
    borderRadius: '6px',
    border: '1px solid #e8e8e8',
  };

  const logStyle: React.CSSProperties = {
    backgroundColor: 'white',
    border: '1px solid #e8e8e8',
    borderRadius: '6px',
    padding: '15px',
    height: '300px',
    overflow: 'auto',
  };

  const logItemStyle: React.CSSProperties = {
    fontSize: '12px',
    color: '#666',
    marginBottom: '4px',
    fontFamily: 'monospace',
  };

  return (
    <div style={containerStyle}>
      {/* 侧边栏 - 显示状态和日志 */}
      <div style={sidebarStyle}>
        <h3>{title}</h3>
        
        {/* 状态显示 */}
        <div style={statusStyle}>
          <h4>状态信息</h4>
          <p>页面就绪: {isPageReady ? '✅ 是' : '❌ 否'}</p>
          <p>容器高度: {containerHeight}px</p>
          <p>当前输入: {currentInput || '(空)'}</p>
          <p>点击次数: {clickCount}</p>
        </div>

        {/* 操作按钮 */}
        <div style={{ marginBottom: '20px' }}>
          <button 
            onClick={clearLogs}
            style={{
              padding: '8px 16px',
              backgroundColor: '#1890ff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              marginRight: '10px',
            }}
          >
            清理日志
          </button>
          {onClose && (
            <button 
              onClick={onClose}
              style={{
                padding: '8px 16px',
                backgroundColor: '#ff4d4f',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              关闭
            </button>
          )}
        </div>

        {/* 事件日志 */}
        <div>
          <h4>事件日志</h4>
          <div style={logStyle}>
            {logs.length === 0 ? (
              <div style={{ color: '#999', fontStyle: 'italic' }}>
                暂无事件日志
              </div>
            ) : (
              logs.map((log, index) => (
                <div key={index} style={logItemStyle}>
                  {log}
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* 主内容区域 - IM智能助手 */}
      <div ref={containerRef} style={mainStyle}>
        <RenderParcel
          setHost={setHost}
          handleInputContainerResize={handleInputContainerResize}
          handleSmartInputChanged={handleSmartInputChanged}
          handleSmartPageClicked={handleSmartPageClicked}
          handleSmartPageLoaded={handleSmartPageLoaded}
        />
      </div>
    </div>
  );
};

export default ExternalPageExample;
