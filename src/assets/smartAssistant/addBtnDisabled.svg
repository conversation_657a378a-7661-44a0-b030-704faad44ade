<?xml version="1.0" encoding="UTF-8"?>
<svg width="64px" height="64px" viewBox="0 0 64 64" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>添加icon</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="64" height="64" rx="8"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="64" height="64" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="全屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="图片助手-超过10个" transform="translate(-1414, -578)">
            <g id="编组-10" transform="translate(690, 566)">
                <g id="添加icon" transform="translate(724, 12)">
                    <use id="矩形备份" stroke="#F2F2F3" mask="url(#mask-2)" stroke-width="2" fill="#FBFBFB" stroke-dasharray="4" xlink:href="#path-1"></use>
                    <rect id="矩形" stroke="#C6C8CA" fill="#C6C8CA" x="24.5" y="31.5" width="15" height="1" rx="0.5"></rect>
                    <rect id="矩形" stroke="#C6C8CA" fill="#C6C8CA" transform="translate(32, 32) rotate(90) translate(-32, -32)" x="24.5" y="31.5" width="15" height="1" rx="0.5"></rect>
                </g>
            </g>
        </g>
    </g>
</svg>