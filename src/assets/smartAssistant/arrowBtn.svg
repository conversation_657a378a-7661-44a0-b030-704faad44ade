<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="64px" viewBox="0 0 24 64" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>箭头</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FAFBFF" offset="0%"></stop>
            <stop stop-color="#E0E8FF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="全屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="多个文档/图片" transform="translate(-702, -578)">
            <g id="箭头" transform="translate(714, 610) rotate(90) translate(-714, -610)translate(682, 598)">
                <rect id="矩形" fill="url(#linearGradient-1)" x="0" y="0" width="64" height="24" rx="4"></rect>
                <path d="M35.8666667,10.4 L32.2202201,14.0464466 C32.0249579,14.2417088 31.7083754,14.2417088 31.5131133,14.0464466 L27.8666667,10.4 L27.8666667,10.4" id="路径" stroke="#575757" stroke-width="1.5" stroke-linecap="round"></path>
            </g>
        </g>
    </g>
</svg>