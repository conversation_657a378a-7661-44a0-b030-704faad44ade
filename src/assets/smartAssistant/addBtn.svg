<?xml version="1.0" encoding="UTF-8"?>
<svg width="64px" height="64px" viewBox="0 0 64 64" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>新增</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="64" height="64" rx="8"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="64" height="64" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="全屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="多个文档/图片" transform="translate(-1414, -578)">
            <g id="新增" transform="translate(1414, 578)">
                <use id="矩形备份" stroke="#DDDEE0" mask="url(#mask-2)" stroke-width="2" stroke-dasharray="4" xlink:href="#path-1"></use>
                <rect id="矩形" fill="#6C6F76" x="24" y="31" width="16" height="2" rx="1"></rect>
                <rect id="矩形" fill="#6C6F76" transform="translate(32, 32) rotate(90) translate(-32, -32)" x="24" y="31" width="16" height="2" rx="1"></rect>
            </g>
        </g>
    </g>
</svg>