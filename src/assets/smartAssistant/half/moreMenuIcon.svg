<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>汉堡图标</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="20" height="20"></rect>
    </defs>
    <g id="OA端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="删除" transform="translate(-1856, -138)">
            <g id="编组-11" transform="translate(1444, 106)">
                <g id="汉堡图标" transform="translate(422, 42) scale(-1, 1) translate(-422, -42)translate(412, 32)">
                    <g id="矩形"></g>
                    <line x1="0.909090909" y1="3.01136373" x2="19.0909091" y2="3.01136373" id="路径" stroke="#575757" stroke-width="2" stroke-linecap="round" ></line>
                    <line x1="0.909090909" y1="9.82954767" x2="14.5454545" y2="9.82954767" id="路径" stroke="#575757" stroke-width="2" stroke-linecap="round" ></line>
                    <line x1="0.909090909" y1="16.6477316" x2="10" y2="16.6477316" id="路径" stroke="#575757" stroke-width="2" stroke-linecap="round" ></line>
                </g>
            </g>
        </g>
    </g>
</svg>