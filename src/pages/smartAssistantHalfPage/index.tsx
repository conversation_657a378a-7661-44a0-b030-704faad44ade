import { SessionType } from '@ht/openim-wasm-client-sdk';
import { useEffect, useState } from 'react';
import { useConversationToggle } from '@/hooks/useConversationToggle';
import { Spin } from '@ht/sprite-ui';
import MessageList from '@/components/Channel/components/MessageList';
import Setting from '@/pages/smartAssistantFullPage/components/Setting';

import { useConversationStore, useSmartAssitantStore } from '@/store';
import { smartAssistantUserID } from '@config/imconfig';
import RobotAnswerSourceRealContent from '@/components/RobotConversation/RobotAnswerSource/realContent';
import RobotOnlineSearchRealContent from '@/components/RobotConversation/RobotOnlineSearch/realContent';
import RobotAnswerSourceModal from '@/components/RobotConversation/RobotAnswerSource/modal';
import { useAudioController } from '@/hooks/useAudioController';
import loadingIcon from '@/assets/smartAssistant/loading.svg';
import {
  getResStyles,
  setResStyles,
  updateResStyles,
} from '@/services/smartAssistant';
import styles from './index.less';
import Header from './components/Header';
import SettingContainer from './components/SettingContainer';
import History from './components/History';
import { defaultStyle, resStyles } from './config';
import OfflineModal from './components/OfflineModal';

const SmartAssistant = () => {
  const [loading, setLoading] = useState(true);
  const { toSpecifiedConversation } = useConversationToggle();
  const [resStyle, setResStyle] = useState(defaultStyle);

  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );
  const isMultiSession = useConversationStore((state) => state.isMultiSession);
  const currentMultiSession = useConversationStore(
    (state) => state.currentMultiSession
  );

  const useConversation = isMultiSession
    ? currentMultiSession
    : currentConversation;

  const conversationList = useConversationStore(
    (state) => state.conversationList
  );

  const conversationIniting = useConversationStore(
    (state) => state.conversationIniting
  );

  // 初始化会话
  useEffect(() => {
    console.debug('conversationIniting', {
      conversationIniting,
      conversationList,
      useConversation,
    });

    const handleConversation = async () => {
      if (conversationIniting) {
        console.log('conversationIniting', conversationIniting);
      } else if (useConversation === undefined) {
        console.log('halfPage handleConversation-1');

        toSpecifiedConversation(
          {
            sourceID: smartAssistantUserID(),
            sessionType: SessionType.Single,
          },
          false,
          () => {
            setLoading(false);
          }
        );
      }
    };
    handleConversation();
  }, [
    conversationList,
    conversationIniting,
    useConversation,
    toSpecifiedConversation,
  ]);

  // 初始化偏好
  useEffect(() => {
    const initResStyle = async () => {
      try {
        const currentResStyles = await getResStyles();

        if (
          currentResStyles?.agentServiceStyle?.style != null &&
          currentResStyles?.agentServiceStyle?.style !== ''
        ) {
          setResStyle(currentResStyles.agentServiceStyle.style);
        } else {
          // 如果接口没有返回，就设置一个默认样式
          await setResStyles({
            agentServiceStyle: {
              style: defaultStyle,
            },
            audioMode: {
              enable: false,
            },
          });
          setResStyle(defaultStyle);
        }
      } catch (e) {
        console.error('初始化样式失败', e);
        // fallback
        setResStyle(defaultStyle);
      }
    };
    initResStyle();
  }, []);

  // 更新样式的方法，传给 Setting
  const handleUpdateResStyle = async (newStyle: typeof defaultStyle) => {
    try {
      await updateResStyles({
        agentServiceStyle: {
          style: newStyle,
        },
        audioMode: {
          enable: false,
        },
      });
      setResStyle(newStyle);
    } catch (e) {
      console.error('更新样式失败', e);
    }
  };

  const rightAreaInSmart = useSmartAssitantStore(
    (state) => state.rightAreaInSmart
  );
  const rightAreaData = useSmartAssitantStore((state) => state.rightAreaData);
  const changeRightArea = useSmartAssitantStore(
    (state) => state.changeRightArea
  );
  const clearRightArea = useSmartAssitantStore((state) => state.clearRightArea);
  const selectObj = useSmartAssitantStore((state) => state.selectObj);
  const setSelectObj = useSmartAssitantStore((state) => state.setSelectObj);

  // 监听来自父页面（门户首页）的设置消息
  useEffect(() => {
    const onListenParentMenuClickedEvent = (event: MessageEvent) => {
      // 可选：检查来源，保证安全
      // if (event.origin !== location.origin) {
      //   return;
      // }

      const { data } = event;

      if (data.type === 'setting') {
        changeRightArea('setting');
      } else if (data.type === 'history') {
        changeRightArea('history');
      }
    };

    window.addEventListener('message', onListenParentMenuClickedEvent);

    // 组件卸载时清理监听
    return () => {
      window.removeEventListener('message', onListenParentMenuClickedEvent);
    };
  }, [changeRightArea]);

  const { state: readingStatus } = useAudioController();
  const { ex } = useConversation || {};

  const handleClicked = () => {
    if (window.parent !== window) {
      window.parent.postMessage(
        {
          type: 'smartPageClicked',
        },
        '*'
      );
    }
  };

  return (
    <div className={styles.smartAssiWrapper} onClick={handleClicked}>
      {loading || useConversation == null ? (
        <div className={styles.spinWrapper}>
          <Spin type="arc" size="large" className={styles.singleSpin} />
        </div>
      ) : (
        <div style={{ width: '100%', height: '100%' }}>
          <div className={styles.smartAssiContent}>
            <Header />
            <div className={styles.content}>
              {readingStatus === 'converting' && (
                <div className={styles.readingStatus}>
                  <img src={loadingIcon}></img>
                  <span>正在生成朗读语音</span>
                </div>
              )}
              <MessageList
                conversation={useConversation}
                isInSmartAssistantPage={true}
                key={`${useConversation?.conversationID || ''}${ex || ''}`}
              />
            </div>
          </div>
          {rightAreaInSmart === 'history' && (
            <History onClose={() => clearRightArea()} />
          )}
          {rightAreaInSmart === 'setting' && (
            <SettingContainer
              title="助手偏好设置"
              height="400px"
              onClose={() => clearRightArea()}
            >
              <Setting
                setResStyle={handleUpdateResStyle}
                resStyle={resStyle}
                resStyles={resStyles}
              />
            </SettingContainer>
          )}
          {rightAreaInSmart === 'resource' && (
            <SettingContainer title="回答来源" onClose={() => clearRightArea()}>
              <RobotAnswerSourceRealContent
                data={rightAreaData?.data}
                activeValue={undefined}
              />
            </SettingContainer>
          )}
          {rightAreaInSmart === 'onlineSearch' && (
            <SettingContainer title="相关网页" onClose={() => clearRightArea()}>
              <RobotOnlineSearchRealContent
                data={rightAreaData?.data}
                activeValue={undefined}
              />
            </SettingContainer>
          )}
          <RobotAnswerSourceModal
            open={selectObj != null}
            selectObj={selectObj}
            handleCancel={() => setSelectObj(undefined)}
          />
        </div>
      )}
      <OfflineModal />
    </div>
  );
};

export default SmartAssistant;
