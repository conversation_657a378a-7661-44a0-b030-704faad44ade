import BasicLayout from '@/layouts/BasicLayout';
import React, { useEffect } from 'react';
import SmartAssistant from '.';

interface RenderParcelProps {
  setHost: (host: any) => void;
  handleInputContainerResize: (data: { height: number }) => void;
  handleSmartInputChanged: (data: string) => void;
  handleSmartPageClicked: () => void;
}

function RenderParcel(props: RenderParcelProps) {
  useEffect(() => {
    console.log('halfPage handleConversation');
  }, []);

  console.debug('parcel props', props);

  return (
    <BasicLayout>
      <SmartAssistant />
    </BasicLayout>
  );
}

export default RenderParcel;
