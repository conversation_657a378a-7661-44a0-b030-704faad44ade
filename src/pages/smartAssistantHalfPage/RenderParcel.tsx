import BasicLayout from '@/layouts/BasicLayout';
import React, {
  useEffect,
  useCallback,
  useRef,
  useImperativeHandle,
  forwardRef,
} from 'react';
import emitter from '@/utils/events';
import SmartAssistant from '.';

// 暴露给外部的消息处理接口
export interface RenderParcelRef {
  // 消息处理函数，供外部调用
  parentInput: (content: string) => void;
  parentSend: (content: string) => void;
}

interface RenderParcelProps {
  setHost: (host: RenderParcelRef) => void;
  handleInputContainerResize: (data: { height: number }) => void;
  handleSmartInputChanged: (data: string) => void;
  handleSmartPageClicked: () => void;
  handleSmartPageLoaded: () => void;
  // 新增的事件处理函数
  handleBackBtnClicked?: () => void;
}

const RenderParcel = forwardRef<RenderParcelRef, RenderParcelProps>(
  (props, ref) => {
    const {
      setHost,
      handleInputContainerResize,
      handleSmartInputChanged,
      handleSmartPageClicked,
      handleSmartPageLoaded,
      handleBackBtnClicked,
    } = props;

    // 事件处理函数
    const onInputContainerResize = useCallback(
      (data: { height: number }) => {
        try {
          handleInputContainerResize(data);
        } catch (error) {
          console.error('处理输入容器尺寸变化事件失败:', error);
        }
      },
      [handleInputContainerResize]
    );

    const onSmartInputChanged = useCallback(
      (data: { content: string }) => {
        try {
          handleSmartInputChanged(data.content);
        } catch (error) {
          console.error('处理智能输入内容变化事件失败:', error);
        }
      },
      [handleSmartInputChanged]
    );

    const onSmartPageClicked = useCallback(() => {
      try {
        handleSmartPageClicked();
      } catch (error) {
        console.error('处理智能助手页面点击事件失败:', error);
      }
    }, [handleSmartPageClicked]);

    const onSmartPageLoaded = useCallback(() => {
      try {
        handleSmartPageLoaded();
      } catch (error) {
        console.error('处理智能助手页面加载完成事件失败:', error);
      }
    }, [handleSmartPageLoaded]);

    const onBackBtnClicked = useCallback(() => {
      try {
        handleBackBtnClicked?.();
      } catch (error) {
        console.error('处理返回按钮点击事件失败:', error);
      }
    }, [handleBackBtnClicked]);

    useEffect(() => {
      console.log('halfPage handleConversation');

      // 订阅外部交互事件
      emitter.on('EXTERNAL_INPUT_CONTAINER_RESIZE', onInputContainerResize);
      emitter.on('EXTERNAL_SMART_INPUT_CHANGED', onSmartInputChanged);
      emitter.on('EXTERNAL_SMART_PAGE_CLICKED', onSmartPageClicked);
      emitter.on('EXTERNAL_SMART_PAGE_LOADED', onSmartPageLoaded);
      emitter.on('EXTERNAL_BACK_BTN_CLICKED', onBackBtnClicked);

      // 清理函数：组件卸载时取消订阅
      return () => {
        emitter.off('EXTERNAL_INPUT_CONTAINER_RESIZE', onInputContainerResize);
        emitter.off('EXTERNAL_SMART_INPUT_CHANGED', onSmartInputChanged);
        emitter.off('EXTERNAL_SMART_PAGE_CLICKED', onSmartPageClicked);
        emitter.off('EXTERNAL_SMART_PAGE_LOADED', onSmartPageLoaded);
        emitter.off('EXTERNAL_BACK_BTN_CLICKED', onBackBtnClicked);
      };
    }, [
      onInputContainerResize,
      onSmartInputChanged,
      onSmartPageClicked,
      onSmartPageLoaded,
      onBackBtnClicked,
      setHost,
    ]);

    console.debug('parcel props', props);

    return (
      <BasicLayout>
        <SmartAssistant />
      </BasicLayout>
    );
  }
);

export default RenderParcel;
