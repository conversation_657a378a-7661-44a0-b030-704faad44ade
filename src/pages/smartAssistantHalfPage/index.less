@import "./breakpoint.less";

.smartAssiWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .smartAssiContent {
    height: 100%;
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    border-radius: 8px;
    overflow: hidden;

    .content {
      padding: 0 28px 20px;

      @media (max-height: @half-collapse-breakpoint2) {
        padding: 0 12px 8px;
      }

      height: 100%;
      position: relative;
    }

    .readingStatus {
      position: absolute;
      top: 120px;
      left: 50%;
      z-index: 990;
      background-color: #fff;
      width: 160px;
      height: 40px;
      box-shadow: 0 2px 12px 0 rgba(13, 26, 38, 12%);
      border-radius: 8px;
      transform: translateX(-50%);
      display: flex;
      align-items: center;
      img {
        width: 16px;
        height: 16px;
        margin-left: 12px;
        margin-right: 8px;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
      span {
        font-size: 14px;
        font-weight: 400;
        color: #1d222c;
      }
    }
  }

  .menuBtn {
    position: absolute;
    top: 24px;
    left: 24px;
    cursor: pointer;
    width: 24px;
    height: 24px;
  }

  .rightAreaContainer {
    width: 480px;
    height: 100%;
    background: #ffffff;
    display: flex;
  }

  .line {
    width: 1px;
    height: 100%;
    background: #f2f2f3;
  }

  .spinWrapper {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: #fff;

    .singleSpin {
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }
  }

  .noPermission {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    > img {
      width: 135px;
    }

    .noPermissionDesc {
      margin-top: 37px;
      font-size: 36px;
      font-weight: 600;
      color: #1d1c1d;
      line-height: 50px;
    }
  }
}
