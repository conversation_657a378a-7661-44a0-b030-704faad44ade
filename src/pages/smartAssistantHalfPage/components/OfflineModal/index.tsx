import { Modal } from '@ht/sprite-ui';

import { useUserStore } from '@/store';
import offlineIcon from '@/assets/smartAssistant/offlineIcon.svg';
import styles from './index.less';

const OfflineModal = () => {
  const onlineState = useUserStore((state) => state.onlineState);

  const handleRefresh = () => {
    window.location.href = location.href;
  };

  return !onlineState ? (
    <Modal
      title={null}
      footer={null}
      closable={false}
      centered={true}
      open={true}
      wrapClassName={styles.offlineContainer}
      width={'100vw'}
      bodyStyle={{
        padding: 0,
      }}
      maskStyle={{ background: 'rgba(255, 255, 255, 70%)' }}
      style={{
        overflow: 'hidden',
        maxWidth: '100vw',
      }}
      maskClosable={false}
      keyboard={true}
    >
      <div className={styles.modalContent}>
        <div className={styles.refreshContainer}>
          <img src={offlineIcon}></img>
          <div>网络异常，请</div>
          <div className={styles.refreshBtn} onClick={handleRefresh}>
            刷新
          </div>
        </div>
      </div>
    </Modal>
  ) : (
    <></>
  );
};

export default OfflineModal;
