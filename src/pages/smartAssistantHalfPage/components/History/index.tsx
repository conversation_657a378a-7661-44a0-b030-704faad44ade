import { FC } from 'react';
import { Drawer } from '@ht/sprite-ui';
import HistoryContent from '@/pages/smartAssistantFullPage/components/History';
import styles from './index.less';

interface HistoryProps {
  onClose: () => void;
}
const History: FC<HistoryProps> = ({ onClose }) => {
  return (
    <Drawer
      placement="bottom"
      height="675px"
      bodyStyle={{
        overflow: 'auto', // 内容超出时可滚动
      }}
      className={styles.smartAssistantHalfHistory}
      open={true}
      closable={false}
      onClose={onClose}
    >
      <HistoryContent />
    </Drawer>
  );
};

export default History;
