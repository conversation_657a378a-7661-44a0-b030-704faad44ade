.settingContainer {
    display: flex;
    flex-direction: column;
    width: calc(100% - 1px);
    background: #ffffff;
    .header {
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 0 23px 0 24px;
    }

    .title {
        height: 24px;
        font-size: 16px;
        font-weight: 600;
        color: #000000;
        line-height: 24px;
    }

    .deleteIcon {
        width: 24px;
        height: 24px;
        cursor: pointer;
    }

    .line {
        width: 100%;
        height: 1px;
        background: #f2f2f3;
    }
    .content {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin: 0 23px 0 24px;
    }

    :global {
        .linkflow-select-single:not(.linkflow-select-customize-input)
        .linkflow-select-selector {
            height: 40px;
            border-radius: 8px;
            padding: 4px 11px;
        }
    }

    .label {
        font-size: 14px;
        font-weight: 400;
        color: #6c6f76;
        line-height: 20px;
        margin: 20px 0 8px;
    }
}
