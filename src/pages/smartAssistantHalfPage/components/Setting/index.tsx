import { Select } from '@ht/sprite-ui';
import closeIcon from '@/assets/closeIcon.svg';
import styles from './index.less';

const { Option } = Select;

interface SettingProps {
  defaultStyle: string;
  setResStyle: any;
  resStyles: { key: string; name: string }[];
  onClose: () => void;
}

const Setting: React.FC<SettingProps> = ({
  defaultStyle,
  setResStyle,
  resStyles,
  onClose,
}) => {
  return (
    <div className={styles.settingContainer}>
      <div className={styles.header}>
        <div className={styles.title}>助手偏好设置</div>
        <img
          src={closeIcon}
          className={styles.deleteIcon}
          onClick={onClose}
        ></img>
      </div>
      <div className={styles.line}></div>
      <div className={styles.content}>
        <div className={styles.label}>服务偏好</div>
        <Select
          defaultValue={defaultStyle}
          style={{ width: 432 }}
          onChange={(value) => {
            setResStyle(value);
          }}
        >
          {resStyles.map((item) => {
            return (
              <Option value={item.key} key={item.key}>
                {item.name}
              </Option>
            );
          })}
        </Select>
      </div>
      <div></div>
    </div>
  );
};

export default Setting;
