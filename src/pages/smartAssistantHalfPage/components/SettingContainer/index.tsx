import React from 'react';
import { Drawer } from '@ht/sprite-ui';

import closeIcon from '@/assets/closeIcon.svg';
import styles from './index.less';

interface SettingContainerProps {
  title: string;
  height?: string;
  onClose: () => void;
}
const SettingContainer: React.FC<SettingContainerProps> = ({
  title,
  onClose,
  children,
  height = '675px',
}) => {
  return (
    <Drawer
      title={title}
      placement="bottom"
      height={height}
      bodyStyle={{
        overflow: 'auto', // 内容超出时可滚动
      }}
      className={styles.settingContainer}
      open={true}
      onClose={onClose}
      closeIcon={
        <img
          src={closeIcon}
          className={styles.deleteIcon}
          onClick={onClose}
        ></img>
      }
    >
      {children}
    </Drawer>
  );
};

export default SettingContainer;
