import { useSmartAssitantStore, useUserStore } from '@/store';
import OIMAvatar from '@/components/OIMAvatar';

import openHistoryIcon from '@/assets/smartAssistant/openHistoryIcon.svg';
import openSettingIcon from '@/assets/smartAssistant/openSettingIcon.svg';
import styles from './index.less';

const LeftArea = () => {
  const { userID, employeeCode, nickname } = useUserStore.getState().selfInfo;

  const changeRightArea = useSmartAssitantStore(
    (state) => state.changeRightArea
  );
  return (
    <div className={styles.leftAreaContainer}>
      <div className={styles.header}>
        <div className={styles.title}>智能助理</div>
        {/* <img src={closeIcon} className={styles.logo}></img> */}
      </div>
      <div className={styles.content}>
        <div
          className={styles.menu}
          onClick={() => {
            changeRightArea('setting');
          }}
        >
          <img src={openSettingIcon} className={styles.menuIcon} alt=""></img>
          <div className={styles.name}>助手偏好设置</div>
        </div>
        <div
          className={styles.menu}
          onClick={() => {
            changeRightArea('history');
          }}
        >
          <img src={openHistoryIcon} className={styles.menuIcon} alt=""></img>
          <div className={styles.name}>查找历史记录</div>
        </div>
      </div>
      <div className={styles.foot}>
        <OIMAvatar
          userID={userID}
          className={styles.avatar}
          size={24}
          hideOnlineStatus={true}
          borderRadius={12}
        />
        <div className={styles.name}>{`${nickname}(${employeeCode})`}</div>
      </div>
    </div>
  );
};

export default LeftArea;
