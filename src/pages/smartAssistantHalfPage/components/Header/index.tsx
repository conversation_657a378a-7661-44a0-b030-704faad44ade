import { useState } from 'react';
import backToFrontIcon from '@/assets/smartAssistant/half/backToFrontIcon.svg';
import fullScreenIcon from '@/assets/smartAssistant/half/fullScreenIcon.svg';
import moreMenuIcon from '@/assets/smartAssistant/half/moreMenuIcon.svg';
import historyIcon from '@/assets/smartAssistant/half/historyIcon.svg';
import settingIcon from '@/assets/smartAssistant/half/settingIcon.svg';
import robotStatic from '@/assets/smartAssistant/robotStatic.png';
import robotGif from '@/assets/smartAssistant/robotGif.gif';
import headBackgroud from '@/assets/smartAssistant/headBackgroud.png';

import { Popover } from '@ht/sprite-ui';
import { useSmartAssitantStore } from '@/store';
import { emit } from '@/utils/events';
import styles from './index.less';

const Header = () => {
  const openFullPage = () => {
    window.open('/linkflow/smartAssistantFullPage', '_blank');
  };

  const changeRightArea = useSmartAssitantStore(
    (state) => state.changeRightArea
  );

  const backToFront = () => {
    if (window.parent !== window) {
      emit('EXTERNAL_BACK_BTN_CLICKED');
    }
  };

  // 是否展示动画
  const [showAnimation, setShowAnimation] = useState(true);

  const onAnimation = () => {
    setShowAnimation(true);
    const timer = setTimeout(() => {
      setShowAnimation(false);
      clearTimeout(timer);
    }, 1600);
  };
  return (
    <div className={styles.header}>
      {/* <img src={headBackgroud} className={styles.headBackgroud} /> */}

      <div
        className={styles.imgBox}
        onMouseEnter={onAnimation}
        onClick={onAnimation}
      >
        {showAnimation ? <img src={robotGif} /> : <img src={robotStatic} />}
      </div>

      <div className={styles.menu}>
        <div className={styles.icon}>
          <img src={backToFrontIcon} onClick={backToFront}></img>
        </div>
        <div className={styles.icon}>
          <img src={fullScreenIcon} onClick={openFullPage}></img>
        </div>
        <div className={styles.icon}>
          <Popover
            showArrow={false}
            trigger={'hover'}
            placement="bottomLeft"
            overlayClassName={styles.popoverWrap}
            content={
              <div className={styles.popoverContent}>
                <div
                  className={styles.menuArea}
                  onClick={() => {
                    changeRightArea('setting');
                  }}
                >
                  <img className={styles.menuIcon} src={settingIcon}></img>
                  <div className={styles.menuTitle}>助手偏好设置</div>
                </div>
                <div
                  className={styles.menuArea}
                  onClick={() => {
                    changeRightArea('history');
                  }}
                >
                  <img className={styles.menuIcon} src={historyIcon}></img>
                  <div className={styles.menuTitle}>查找历史记录</div>
                </div>
              </div>
            }
          >
            <img src={moreMenuIcon}></img>
          </Popover>
        </div>
      </div>
    </div>
  );
};

export default Header;
