@import "src/pages/smartAssistantHalfPage/breakpoint.less";

.header {
  width: 100%;
  padding: 15px 28px 40px;
  display: flex;
  align-items: center;

  @media (max-height: @half-collapse-breakpoint2) {
    min-height: 34px;
    padding: 10px 16px 7px;
  }

  // .headBackgroud {
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   width: 100%;
  //   height: 70px;
  // }
  .imgBox {
    img {
      width: 50px;
    }
  }
  .title {
    font-size: 14px;
    font-weight: 600;
    color: #000000;
    line-height: 20px;
  }

  .menu {
    flex: 1;
    display: flex;
    align-items: center;

    @media (max-height: @half-collapse-breakpoint2) {
      display: none;
    }
    .icon {
      &:nth-child(1) {
        flex: 1;
      }

      margin-left: 20px;

      img {
        width: 20px;
        height: 20px;
        cursor: pointer;
        float: right;
      }
    }
  }
}

.popoverWrap {
  :global {
    .linkflow-popover-inner-content {
      border-radius: 8px;
      color: #fff !important;
      padding: 0;
    }
  }
  .popoverContent {
    width: 160px;
    height: 88px;
    background: #ffffff;
    // background-color: green;
    box-shadow: 0 2px 12px 0 rgba(13, 26, 38, 12%);
    border-radius: 8px;
    padding: 4px 0;

    .menuArea {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      cursor: pointer;

      &:hover {
        background: #f2f2f3;
      }
    }

    .menuIcon {
      margin-left: 12px;
      width: 20px;
      height: 20px;
    }

    .menuTitle {
      font-size: 14px;
      font-weight: 400;
      color: #1d222c;
      margin-left: 6px;
    }
  }
}
