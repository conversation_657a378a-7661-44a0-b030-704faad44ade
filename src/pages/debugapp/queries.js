// if (globalThis.performance == null) {
//  globalThis.performance = require('perf_hooks').performance;
// }

function uid(i) {
  // This will make ids of different lengths, but we want to inject
  // some larger data than just ints (something like a uuid) but we
  // don't want to actually generate uuids because that's slow-ish and
  // we want profiling to show sqlite as much as possible
  return `0000000000000000000000000${i}`;
}

function formatNumber(num) {
  return new Intl.NumberFormat('en-US').format(num);
}

async function clear(db, output = console.log) {
  output('Clearing existing data');
  db.exec(`
    BEGIN TRANSACTION;
    DROP TABLE IF EXISTS kv;
    CREATE TABLE kv (key TEXT, value TEXT);
    COMMIT;
  `);
  output('Done');
}

async function clearChatlog(db, output = console.log) {
  output(`Clearing data`);
  db.exec(`
    BEGIN TRANSACTION;
    DROP TABLE IF EXISTS chat_logs_benchmark;
    CREATE TABLE chat_logs_benchmark (
       'client_msg_id' char(32),
        'server_msg_id' char(32),
        'send_id' char(32),
        'recv_id' char(32),
        'sender_platform_id' smallint,
        'sender_nick_name' varchar(255),
        'sender_face_url' varchar(255),
        'session_type' smallint,
        'msg_from' smallint,
        'content_type' smallint,
        'content' varchar(1000),
        'is_read' tinyint(1),
        'status' smallint,
        'seq' int DEFAULT 0,
        'send_time' int,
        'create_time' int,
        'attached_info' varchar(1024),
        'ex' varchar(1024),
        'local_ex' varchar(1024),
        'is_react' tinyint(1),
        'is_external_extensions' tinyint(1),
        'msg_first_modify_time' int,
        PRIMARY KEY ('client_msg_id')
    );
    CREATE INDEX idx_seq on chat_logs_benchmark(seq);
    COMMIT;
  `);
  output('Done');
}

function generateRandomID() {
  return Array.from(crypto.getRandomValues(new Uint8Array(16)))
    .map((b) => b.toString(16).padStart(2, '0'))
    .join('');
}

function mockChat(db, count, output = console.log, outputTiming = console.log) {
  const start = Date.now();
  db.exec('BEGIN TRANSACTION');
  const stmt = db.prepare(`INSERT INTO chat_logs_benchmark VALUES (
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
        ?, ?
      )`);

  output(`Inserting ${formatNumber(count)} items`);

  // 从当前最大seq开始 (需要先查询已有数据)
  const seqResult = db.exec(
    'SELECT MAX(seq) as max_seq FROM chat_logs_benchmark'
  );
  let seq = seqResult.length > 0 ? seqResult[0].values[0][0] + 1 : 0;
  const baseTime = Date.now();

  for (let i = 0; i < count; i++) {
    const clientMsgId = generateRandomID();
    const serverMsgId = generateRandomID();
    const timeOffset = i * 1000;

    stmt.bind([
      clientMsgId,
      serverMsgId,
      '011114', // 固定发送者ID
      '467392957', // 固定接收者ID
      5, // 固定sender_platform_id
      '曹啸',
      'https://example.com/avatar1.jpg',
      3, // session_type
      100, // msg_from
      101, // content_type
      JSON.stringify({
        content:
          i % 2 === 0
            ? '可以为什么我们这个聊天框，滚动起来会感觉卡卡的，slack好像不太会'
            : '好了左边菜单的thread可以不用展示吗',
      }), // 交替内容
      0, // is_read
      2, // 固定status
      seq++, // 自增序列
      baseTime + timeOffset, // send_time
      baseTime + timeOffset - 576, // create_time
      JSON.stringify({
        groupHasReadInfo: { hasReadCount: 0, groupMemberCount: 15 },
        isPrivateChat: false,
        burnDuration: 0,
      }),
      '', // ex
      '', // local_ex
      0, // is_react
      0, // is_external_extensions
      baseTime + timeOffset, // msg_first_modify_time
    ]);

    stmt.step();
    stmt.reset();
  }
  db.exec('COMMIT');
  const took = Date.now() - start;
  output(`Done! Took: ${took}`);
  outputTiming(took);
}

function populate(db, count, output = console.log, outputTiming = console.log) {
  const start = Date.now();
  db.exec('BEGIN TRANSACTION');
  const stmt = db.prepare('INSERT INTO kv (key, value) VALUES (?, ?)');

  output(`Inserting ${formatNumber(count)} items`);

  for (let i = 0; i < count; i++) {
    stmt.run([uid(i), Math.floor(Math.random() * 100).toString()]);
  }
  db.exec('COMMIT');
  const took = Date.now() - start;
  output(`Done! Took: ${took}`);
  outputTiming(took);
}

function sumAll(db, output = console.log, outputTiming = console.log) {
  output('Running <code>SELECT COUNT(*) FROM kv</code>');

  let stmt;
  try {
    stmt = db.prepare(`SELECT SUM(value) FROM kv`);
  } catch (err) {
    output(`Error (make sure you write data first): ${err.message}`);
    throw err;
  }

  const start = performance.now();
  let row;

  if (stmt.all) {
    const row = stmt.all();
    output(JSON.stringify(row));
  } else {
    while (stmt.step()) {
      row = stmt.getAsObject();
    }
    stmt.free();
  }

  const took = performance.now() - start;
  output(`<code>${JSON.stringify(row)}</code>`);

  outputTiming(took);
  output(`Done reading, took ${formatNumber(took)}ms`);
  output('That scanned through all of the data');
}

function executeSql(
  db,
  sql,
  output = console.log,
  outputTiming = console,
  limit = 100
) {
  const limitedSql = addLimitIfNeeded(sql, limit);
  if (limitedSql !== sql) {
    output(`Query results are limited to ${limit} rows.`);
  }
  output(`Running <code>${limitedSql}</code>`);
  const start = Date.now();
  const results = db.exec(limitedSql);
  const took = Date.now() - start;
  outputTiming(took);
  output(`Done exec sql, took ${formatNumber(took)}ms`);
  return results;
}

function addLimitIfNeeded(sql, limit) {
  if (/^\s*SELECT/i.test(sql) && !/LIMIT\s+\d+/i.test(sql)) {
    return `${sql} LIMIT ${limit}`;
  }
  return sql;
}

async function randomReads(
  db,
  output = console.log,
  outputTiming = console.log
) {
  output(
    'Running <code>SELECT key FROM kv LIMIT 1000 OFFSET ?</code> 20 times with increasing offset'
  );
  const start = Date.now();

  let stmt;
  try {
    stmt = db.prepare(`SELECT key FROM kv LIMIT 1000 OFFSET ?`);
  } catch (err) {
    output(`Error (make sure you write data first): ${err.message}`);
    throw err;
  }

  const canRebind = !!stmt.reset;

  for (let i = 0; i < 20; i++) {
    const off = i * 300;
    if (canRebind) {
      stmt.bind([off]);
    }
    // output('Using offset: ' + formatNumber(off));

    if (stmt.all) {
      // better-sqlite3 doesn't allow you to rebind the same
      // statement. This is probably a tiny perf hit, but negligable
      // for what we're measuring (it's already so much faster anyway)
      stmt = db.prepare(`SELECT key FROM kv LIMIT 2000 OFFSET ${off}`);
      const rows = stmt.all();
    } else {
      let num = 0;
      while (stmt.step()) {
        num++;
        const row = stmt.get();
        if (num === 999) {
          // output('(999 items hidden)');
        } else if (num > 998) {
          // output('<code>' + JSON.stringify(row) + '</code>');
        }
      }
    }

    if (canRebind) {
      stmt.reset();
    }
  }

  if (stmt.free) {
    stmt.free();
  }

  const took = Date.now() - start;
  outputTiming(took);
  output(`Done reading, took ${formatNumber(took)}ms`);
}

module.exports = {
  clear,
  populate,
  sumAll,
  randomReads,
  clearChatlog,
  mockChat,
  executeSql,
};
