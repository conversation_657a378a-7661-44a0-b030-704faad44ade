.emptyPage {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  background-color: #efefef;
  .content {
    max-width: 1920px;
    width: 1920px;
    min-width: 500px;
    margin: 0 auto;
    box-sizing: border-box;
    display: flex;
    background: linear-gradient(
      360deg,
      #efefef 0%,
      #efefef 35%,
      #e0e7f0 48%,
      #dae5f0 56%,
      #d9e4f0 67%,
      #dae6f1 79%,
      #f7fafd 100%
    );
    opacity: 0.8;
  }
  .left,
  .right {
    height: 100%;
  }

  .left {
    flex: 1;
  }

  .right {
    height: calc(100% - 122px);
    margin-top: 106px;
    margin-bottom: 16px;
    width: 460px;
    background: #ffffff;
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 4%);
    border-radius: 12px;
  }
}
