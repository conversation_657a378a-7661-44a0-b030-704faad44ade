import { CSSProperties } from 'react';
import { OnlineState } from '@ht/openim-wasm-client-sdk';
import useOnlineStatus from '@/hooks/useOnlineStatus';

import classnames from 'classnames';
import styles from './index.less';

interface OnlineOrTypingStatusProps {
  userID: string;
  size?: number;
  showText?: boolean;
  style?: CSSProperties;
  textStyle?: CSSProperties;
}

const OnlineOrTypingStatus = ({
  userID,
  size = 9,
  showText = false,
  style,
  textStyle,
}: OnlineOrTypingStatusProps) => {
  const { loading, onlineState } = useOnlineStatus(userID);

  // 设置默认 height 为 10px
  const mergedStyle = {
    height: '10px', // 默认值
    ...style, // 覆盖默认值
  };

  const mergedTextStyle = {
    marginLeft: '16px', // 默认值
    ...textStyle, // 覆盖默认值
  };

  let text = '';
  if (!loading) {
    if (onlineState?.status === OnlineState.Online) {
      text = '在线';
    } else {
      text = '离线';
    }
  }

  return (
    <div className={styles.onlineStatus} style={mergedStyle}>
      <div
        className={classnames(
          styles.state,
          onlineState?.status === OnlineState.Online
            ? styles.online
            : styles.offline
        )}
        style={{ width: size, height: size }}
      />
      {showText && <div style={mergedTextStyle}>{text}</div>}
    </div>
  );
};

export default OnlineOrTypingStatus;
