import { SessionType } from '@ht/openim-wasm-client-sdk';
import { useEffect, useState } from 'react';
import { useConversationToggle } from '@/hooks/useConversationToggle';
import { Spin } from '@ht/sprite-ui';
import MessageList from '@/components/Channel/components/MessageList';
import {
  useConversationStore,
  useSmartAssitantStore,
  useUserStore,
} from '@/store';
import RobotAnswerSource from '@/components/RobotConversation/RobotAnswerSource';
import RobotOnlineSearch from '@/components/RobotConversation/RobotOnlineSearch';
import RobotAnswerSourceModal from '@/components/RobotConversation/RobotAnswerSource/modal';
import { smartAssistantUserID } from '@config/imconfig';
import { useAudioController } from '@/hooks/useAudioController';
import loadingIcon from '@/assets/smartAssistant/loading.svg';
import {
  getResStyles,
  setResStyles,
  updateResStyles,
} from '@/services/smartAssistant';
import ResponsiveLayout from './components/ResonsiveLayout';
import styles from './index.less';
import LeftArea from './components/LeftArea';
import Setting from './components/Setting';
import History from './components/History';
import SettingContainer from './components/SettingContainer';
import { defaultStyle, resStyles } from '../smartAssistantHalfPage/config';
import OfflineModal from '../smartAssistantHalfPage/components/OfflineModal';

const SmartAssistant = () => {
  const [loading, setLoading] = useState(true);
  const { toSpecifiedConversation } = useConversationToggle();
  const [resStyle, setResStyle] = useState(defaultStyle);

  const [showMenu, setShowMenu] = useState(true);
  const { state: readingStatus } = useAudioController();

  const rightAreaInSmart = useSmartAssitantStore(
    (state) => state.rightAreaInSmart
  );
  const rightAreaData = useSmartAssitantStore((state) => state.rightAreaData);
  const clearRightArea = useSmartAssitantStore((state) => state.clearRightArea);

  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );

  const isMultiSession = useConversationStore((state) => state.isMultiSession);
  const currentMultiSession = useConversationStore(
    (state) => state.currentMultiSession
  );

  const useConversation = isMultiSession
    ? currentMultiSession
    : currentConversation;

  const selectObj = useSmartAssitantStore((state) => state.selectObj);
  const setSelectObj = useSmartAssitantStore((state) => state.setSelectObj);

  const onlineState = useUserStore((state) => state.onlineState);
  // 初始化会话
  useEffect(() => {
    const handleConversation = async () => {
      toSpecifiedConversation(
        {
          sourceID: smartAssistantUserID(),
          sessionType: SessionType.Single,
        },
        false,
        () => {
          setLoading(false);
        }
      );
    };
    handleConversation();
  }, []);

  // 初始化偏好
  useEffect(() => {
    const initResStyle = async () => {
      try {
        const currentResStyles = await getResStyles();
        if (
          currentResStyles?.agentServiceStyle?.style != null &&
          currentResStyles?.agentServiceStyle?.style !== ''
        ) {
          setResStyle(currentResStyles.agentServiceStyle.style);
        } else {
          await setResStyles({
            agentServiceStyle: {
              style: defaultStyle,
            },
            audioMode: {
              enable: false,
            },
          });
          setResStyle(defaultStyle);
        }
      } catch (e) {
        console.error('初始化样式失败', e);
        setResStyle(defaultStyle);
      }
    };
    initResStyle();
  }, []);

  // 更新偏好
  const handleUpdateResStyle = async (newStyle: typeof defaultStyle) => {
    try {
      await updateResStyles({
        agentServiceStyle: {
          style: newStyle,
        },
        audioMode: {
          enable: false,
        },
      });
      setResStyle(newStyle);
    } catch (e) {
      console.error('更新样式失败', e);
    }
  };

  const { ex } = useConversation || {};
  return (
    <div className={styles.smartAssiWrapper}>
      {loading || useConversation == null ? (
        <div className={styles.spinWrapper}>
          <Spin type="arc" size="large" className={styles.singleSpin} />
        </div>
      ) : (
        <div style={{ width: '100%', height: '100%' }}>
          <ResponsiveLayout
            leftSidebar={<LeftArea />}
            isLeftOpen={showMenu}
            isRightOpen={rightAreaInSmart != null}
            rightSidebar={
              <div className={styles.rightAreaContainer}>
                <div className={styles.line}></div>
                <div style={{ width: 479, background: '#fff' }}>
                  {rightAreaInSmart === 'history' && <History />}
                  {rightAreaInSmart === 'setting' && (
                    <SettingContainer
                      title="助手偏好设置"
                      onClose={clearRightArea}
                    >
                      <Setting
                        setResStyle={handleUpdateResStyle}
                        resStyle={resStyle}
                        resStyles={resStyles}
                      />
                    </SettingContainer>
                  )}

                  {rightAreaInSmart === 'resource' && (
                    <RobotAnswerSource
                      data={rightAreaData?.data}
                      activeValue={undefined}
                      onClose={clearRightArea}
                    />
                  )}

                  {rightAreaInSmart === 'onlineSearch' && (
                    <RobotOnlineSearch
                      data={rightAreaData?.data}
                      activeValue={undefined}
                      onClose={clearRightArea}
                      title="相关网页"
                    />
                  )}
                </div>
              </div>
            }
          >
            <div className={styles.smartAssiContent}>
              {readingStatus === 'converting' && (
                <div className={styles.readingStatus}>
                  <img src={loadingIcon}></img>
                  <span>正在生成朗读语音</span>
                </div>
              )}
              <MessageList
                conversation={useConversation}
                isInSmartAssistantPage={true}
                key={`${useConversation?.conversationID || ''}${ex || ''}`}
              />
            </div>
          </ResponsiveLayout>
          <RobotAnswerSourceModal
            open={selectObj != null}
            selectObj={selectObj}
            handleCancel={() => setSelectObj(undefined)}
          />
          <OfflineModal />
        </div>
      )}
    </div>
  );
};
export default SmartAssistant;
