import React, { useState, useEffect } from 'react';
import styles from './index.less';

interface LayoutProps {
  leftSidebar?: React.ReactNode;
  isLeftOpen: boolean; // 这个其实没用了，可以保留兼容
  isRightOpen: boolean;
  rightSidebar?: React.ReactNode;
  children: React.ReactNode;
}

export default function ResponsiveLayout({
  leftSidebar,
  isLeftOpen, // 保留但无实际作用
  rightSidebar,
  isRightOpen,
  children,
}: LayoutProps) {
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  const LEFT_WIDTH = 260;
  const RIGHT_WIDTH = 480;
  const MIN_CONTENT = 500;

  // 动态 breakpoint（只考虑右侧抽屉）
  const getBreakpoint = () => {
    return LEFT_WIDTH + MIN_CONTENT + (rightSidebar ? RIGHT_WIDTH : 0);
  };

  useEffect(() => {
    const onResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', onResize);
    return () => window.removeEventListener('resize', onResize);
  }, []);

  const breakpoint = getBreakpoint();
  const isMobile = windowWidth < breakpoint;

  return (
    <div className={styles.layout}>
      {/* 左侧 sidebar 永远固定 */}
      {leftSidebar && (
        <div className={`${styles.sidebar} ${styles.left}`}>{leftSidebar}</div>
      )}

      {/* 中间内容 */}
      <div className={styles.content}>
        <div className={styles.contentInner}>{children}</div>
      </div>

      {/* 右侧 sidebar 或 drawer */}
      {rightSidebar && !isMobile && isRightOpen && (
        <div className={`${styles.sidebar} ${styles.right}`}>
          {rightSidebar}
        </div>
      )}
      {rightSidebar && isMobile && isRightOpen && (
        <div className={`${styles.drawer} ${styles.right} ${styles.open}`}>
          {rightSidebar}
        </div>
      )}
    </div>
  );
}
