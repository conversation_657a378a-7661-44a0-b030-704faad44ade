@left-width: 260px;
@right-width: 480px;
@min-content: 500px;
@max-content: 1100px;

.layout {
  display: flex;
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden; // 保证整体不会乱跑
}

/* 左右 sidebar，固定宽度 */
.sidebar {
  height: 100%;
  flex-shrink: 0;

  &.left {
    width: @left-width; // 永远固定
  }

  &.right {
    width: @right-width;
  }
}

/* 中间区域 */
.content {
  position: relative;
  flex: 1;
  display: flex;
  justify-content: center; // 内容水平居中
  align-items: flex-start;
  padding: 16px;
  overflow-x: auto; // 横向滚动
  overflow-y: auto; // 纵向滚动
}

/* 中间内容 */
.contentInner {
  width: 100%;
  height: 100%;
  min-width: @min-content;
  max-width: @max-content;
  box-sizing: border-box;
}

/* 右侧抽屉模式 */
.drawer {
  position: fixed;
  top: 0;
  height: 100%;
  box-shadow: 2px 0 6px rgba(0, 0, 0, 10%);
  z-index: 100;

  &.left {
    display: none; // 左边永远不用抽屉
  }

  &.right {
    right: 0;
    width: @right-width;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    &.open {
      transform: translateX(0);
    }
  }
}

/* 控制按钮（右边可用，左边去掉也行） */
.toggleBtn {
  position: absolute;
  top: 16px;
  background: #1677ff;
  color: #fff;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  z-index: 120;

  &.left {
    display: none; // 左边没必要有按钮了
  }

  &.right {
    right: 16px;
  }
}
