import { FC, useEffect, useState } from 'react';
import classNames from 'classnames';
import { Input } from '@ht/sprite-ui';
import searchIcon from '@/assets/smartAssistant/historySearch.png';
import searchClearIcon from '@/assets/smartAssistant/searchClear.svg';
import { HistoryTabType } from '../..';
import styles from './index.less';

interface SearchAndTabsProps {
  onInputChange: (val: string) => void;
  currentTab: HistoryTabType;
  onTabChange?: (tab: HistoryTabType) => void;
}

interface tabItemProps {
  key: HistoryTabType;
  title: string;
}

const HistoryTab: tabItemProps[] = [
  {
    key: 'message',
    title: '对话',
  },
  {
    key: 'picture',
    title: '图片',
  },
  {
    key: 'file',
    title: '文件',
  },
];
const SearchAndTabs: FC<SearchAndTabsProps> = (props) => {
  const { onInputChange, currentTab, onTabChange } = props;
  const [inputvalue, setInputValue] = useState<any>();
  const [lock, setLock] = useState<boolean>(false);

  const handleInputChange = (e: any) => {
    if (e.type === 'compositionstart') {
      setLock(true);
      return;
    }
    setInputValue(e.target.value.trim());
    if (e.type === 'compositionend') {
      onInputChange(e.target.value.trim());
      setLock(false);
    }
    if (!lock) {
      onInputChange(e.target.value.trim());
    }
  };

  useEffect(() => {
    setLock(false);
  }, [currentTab]);

  return (
    <div className={styles.searchAndTabsWrap}>
      <div className={styles.searchInputWrap}>
        <div className={styles.iconContainer}>
          <img src={searchIcon} />
        </div>
        <Input
          value={inputvalue}
          onChange={handleInputChange}
          onCompositionStart={handleInputChange}
          onCompositionEnd={handleInputChange}
          placeholder={'查找聊天内容'}
          style={{ width: '100%' }}
          className={styles.searchInput}
        />
        <div
          className={styles.deleteIcon}
          onClick={(e) => {
            e.stopPropagation();
            setInputValue('');
            onInputChange('');
          }}
        >
          {inputvalue != null && inputvalue !== '' && (
            <img src={searchClearIcon}></img>
          )}
        </div>
      </div>
      <div className={styles.tabsWrap}>
        {HistoryTab.map((item) => {
          return (
            <div
              key={item.key}
              className={classNames(
                styles.tabItem,
                currentTab === item.key && styles.active
              )}
              onClick={() => {
                onTabChange?.(item.key);
              }}
            >
              <span>{item.title}</span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default SearchAndTabs;
