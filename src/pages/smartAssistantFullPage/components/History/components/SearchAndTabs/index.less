.searchAndTabsWrap {
  .searchInputWrap {
    position: relative;
    margin-bottom: 12px;

    .iconContainer {
      position: absolute;
      left: 16px;
      top: 0;
      z-index: 1;
      height: 40px;
      display: flex;
      align-items: center;
    }

    .searchInput {
      height: 40px;
      background: #ffffff;
      border-radius: 8px;
      border: 1px solid #d7d7d7;
      padding: 0 44px;
      color: #1d222c;
    }

    .deleteIcon {
      position: absolute;
      right: 16px;
      top: 0;
      height: 40px;
      z-index: 1;
      display: flex;
      align-items: center;
      cursor: pointer;
    }

    :global {
      .linkflow-input:focus,
      .linkflow-input-focused {
        border: 1px solid #2962ff !important;
      }
      .linkflow-input::placeholder {
        color: #999999;
      }
    }
  }

  .tabsWrap {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .tabItem {
      width: 60px;
      height: 32px;
      border-radius: 8px;
      font-size: 14px;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #dddee0;
      color: #3f434b;
      margin-right: 16px;
      cursor: pointer;

      &.active {
        background: #2962ff;
        border: 1px solid #2962ff;
        color: #ffffff;
      }
    }
  }
}
