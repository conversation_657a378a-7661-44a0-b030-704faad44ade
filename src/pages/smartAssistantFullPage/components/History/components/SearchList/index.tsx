import { FC } from 'react';
import MessageList from '@/components/ChannelHistory/components/MessageList';
import CloudList from '@/components/ChannelHistory/components/MessageList/OtherList';
import { HistoryTabType } from '../..';

interface SearchListProps {
  searchFilterParams: any;
  currentTab: HistoryTabType;
}

const SearchList: FC<SearchListProps> = ({
  currentTab,
  searchFilterParams,
}) => {
  return currentTab === 'message' ? (
    <MessageList
      searchFilterParams={searchFilterParams}
      currentTab={currentTab}
    />
  ) : (
    <CloudList
      searchFilterParams={searchFilterParams}
      currentTab={currentTab}
    />
  );
};

export default SearchList;
