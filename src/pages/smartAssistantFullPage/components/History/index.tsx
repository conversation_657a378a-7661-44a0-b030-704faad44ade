import { useCallback, useState } from 'react';
import { throttle } from 'lodash';
import { useSmartAssitantStore } from '@/store';
import SearchAndTabs from './components/SearchAndTabs';
import SearchList from './components/SearchList';
import SettingContainer from '../SettingContainer';
import styles from './index.less';

export type HistoryTabType = 'message' | 'file' | 'picture';
const History = () => {
  const [currentTab, setCurrentTab] = useState<HistoryTabType>('message');
  const [searchFilterParams, setSearchFilterParams] = useState({
    searchValue: '',
    searchTimePosition: 0, // 需要有默认值
    searchTimePeriod: 0, // 需要有默认值
  });

  const clearRightArea = useSmartAssitantStore((state) => state.clearRightArea);

  const onSearchFilterParamsChange = (params: any) => {
    setSearchFilterParams((pre) => {
      return {
        ...pre,
        ...params,
      };
    });
  };

  const onInputChangeThrottle = useCallback(
    throttle(
      (val) => {
        onSearchFilterParamsChange({
          searchValue: val,
        });
      },
      500,
      { leading: false, trailing: true }
    ),
    []
  );
  return (
    <div className={styles.historyContentWrap}>
      <SettingContainer title="查找聊天记录" onClose={clearRightArea}>
        <SearchAndTabs
          onInputChange={(val) => {
            onInputChangeThrottle(val);
          }}
          currentTab={currentTab}
          onTabChange={(val: HistoryTabType) => {
            setCurrentTab(val);
          }}
        />
      </SettingContainer>

      <SearchList
        searchFilterParams={searchFilterParams}
        currentTab={currentTab}
      />
    </div>
  );
};

export default History;
