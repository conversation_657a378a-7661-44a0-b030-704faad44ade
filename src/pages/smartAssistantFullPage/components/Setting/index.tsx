import { Select } from '@ht/sprite-ui';
import styles from './index.less';

const { Option } = Select;

interface SettingProps {
  resStyle: string;
  setResStyle: any;
  resStyles: { key: string; name: string }[];
}

const Setting: React.FC<SettingProps> = ({
  resStyle,
  setResStyle,
  resStyles,
}) => {
  return (
    <>
      <div className={styles.label}>服务偏好</div>
      <Select
        value={resStyle}
        onChange={(value) => {
          setResStyle(value);
        }}
        className={styles.select}
      >
        {resStyles.map((item) => {
          return (
            <Option value={item.key} key={item.key}>
              {item.name}
            </Option>
          );
        })}
      </Select>
    </>
  );
};

export default Setting;
