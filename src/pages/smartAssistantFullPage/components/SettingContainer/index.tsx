import closeIcon from '@/assets/closeIcon.svg';
import styles from './index.less';

interface SettingContainerProps {
  title: string;
  onClose: () => void;
}

const SettingContainer: React.FC<SettingContainerProps> = ({
  title,
  onClose,
  children,
}) => {
  return (
    <div className={styles.settingContainer}>
      <div className={styles.header}>
        <div className={styles.title}>{title}</div>
        <img
          src={closeIcon}
          className={styles.deleteIcon}
          onClick={onClose}
        ></img>
      </div>
      <div className={styles.line}></div>
      <div className={styles.content}>{children}</div>
    </div>
  );
};

export default SettingContainer;
