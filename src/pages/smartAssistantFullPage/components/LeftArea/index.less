.leftAreaContainer {
  width: 100%;
  height: 100%;
  background: #f2f2f3;
  border: 1px solid #dddee0;
  padding: 23px 15px 40px 23px;
  display: flex;
  flex-direction: column;

  .header {
    display: flex;
    // height: 28px;
    align-items: start;
    justify-content: space-between;
  }
  .title {
    font-size: 20px;
    font-weight: 600;
    color: #000000;
    line-height: 28px;
  }

  .imgBox {
    img {
      width: 64px;
    }
  }

  .logo {
    width: 24px;
    height: 24px;
    cursor: pointer;
  }

  .content {
    margin-top: 20px;
    flex: 1;
  }

  .menu {
    display: flex;
    width: 100%;
    height: 40px;
    cursor: pointer;
    align-items: center;
    margin-top: 12px;

    &:hover {
      // background-color: red;
    }
    .name {
      font-size: 14px;
      font-weight: 400;
      color: #1d222c;
      line-height: 22px;
    }

    .menuIcon {
      margin-right: 6px;
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
  }
  .foot {
    display: flex;
    align-items: center;
    height: 24px;

    .name {
      margin-left: 12px;
      font-size: 14px;
      font-weight: 400;
      color: #000000;
      line-height: 18px;
    }
  }
}
