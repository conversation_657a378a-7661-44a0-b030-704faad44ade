import { ChooseModalState } from '@/components/ChooseModal';
import { CheckListItem } from '@/components/ChooseModal/ChooseBox/CheckItem';
import mitt from 'mitt';
import {
  ConversationItem,
  GroupItem,
  GroupMemberItem,
  MessageItem,
} from '@ht/openim-wasm-client-sdk/lib/types/entity';
import { InviteData } from '@/components/RtcCallModal/data';

// 外部交互事件类型定义
export type ExternalInteractionEvents = {
  // 输入容器尺寸变化事件
  EXTERNAL_INPUT_CONTAINER_RESIZE: {
    height: number;
  };

  // 智能输入内容变化事件
  EXTERNAL_SMART_INPUT_CHANGED: {
    content: string;
  };

  // 智能助手页面点击事件
  EXTERNAL_SMART_PAGE_CLICKED: void;

  // 智能助手页面加载完成事件（预留）
  EXTERNAL_SMART_PAGE_LOADED: void;
};

type EmitterEvents = {
  // 内部事件
  OPEN_USER_CARD: OpenUserCardParams;
  OPEN_GROUP_CARD: GroupItem;
  OPEN_CHOOSE_MODAL: ChooseModalState;
  CHAT_LIST_SCROLL_TO_BOTTOM: {
    conversation?: ConversationItem;
    // immediate?: boolean;
  };
  OPEN_RTC_MODAL: InviteData;
  // message store
  PUSH_NEW_MSG: MessageItem;
  BATCH_PUSH_NEW_MSGS: MessageItem[];
  UPDATE_ONE_MSG: MessageItem;
  UPDATE_OR_INSERT_ONE_MSG: MessageItem;
  UPDATE_MSG_NICK_AND_FACEURL: UpdateMessaggeBaseInfoParams;
  DELETE_ONE_MSG: string;
  LOAD_HISTORY_MSGS: void;
  GET_MSG_LIST: (messages: MessageItem[]) => void;
  CLEAR_MSGS: void;
  CLEAR_MSG_STATE: keyof MessageItem;

  SCROLL_TO_MESSAGE: {
    message: MessageItem;
    conversationID: string;
  };

  SELECT_USER: SelectUserParams;
  CLOSE_SEARCH_MODAL: void;
  TRIGGER_GROUP_AT: GroupMemberItem;
  REPLAYMSG: MessageItem;
  REFRESHEMOJIS: void;

  // 外部交互事件
  EXTERNAL_INPUT_CONTAINER_RESIZE: ExternalInteractionEvents['EXTERNAL_INPUT_CONTAINER_RESIZE'];
  EXTERNAL_SMART_INPUT_CHANGED: ExternalInteractionEvents['EXTERNAL_SMART_INPUT_CHANGED'];
  EXTERNAL_SMART_PAGE_CLICKED: ExternalInteractionEvents['EXTERNAL_SMART_PAGE_CLICKED'];
  EXTERNAL_SMART_PAGE_LOADED: ExternalInteractionEvents['EXTERNAL_SMART_PAGE_LOADED'];
};

export type SelectUserParams = {
  notConversation: boolean;
  choosedList: CheckListItem[];
};

export type OpenUserCardParams = {
  userID?: string;
  groupID?: string;
  isSelf?: boolean;
  notAdd?: boolean;
};

export type UpdateMessaggeBaseInfoParams = {
  sendID: string;
  senderNickname: string;
  senderFaceUrl: string;
};

const emitter = mitt<EmitterEvents>();

export const { emit } = emitter;

export default emitter;
