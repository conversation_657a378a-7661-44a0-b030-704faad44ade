/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable eslint-comments/no-duplicate-disable */
/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable indent */
/* eslint-disable no-case-declarations */
/* eslint-disable complexity */
/* eslint-disable array-callback-return */
/* eslint-disable max-lines */
import wordIcon from '@/assets/channel/clouddocument/word.svg';
import excelIcon from '@/assets/channel/clouddocument/excel.svg';
import pptIcon from '@/assets/channel/clouddocument/ppt.svg';
import pdfIcon from '@/assets/channel/clouddocument/pdf.svg';
import mindIcon from '@/assets/channel/clouddocument/mind.svg';
import markdownIcon from '@/assets/channel/clouddocument/markdown.svg';
import flowIcon from '@/assets/channel/clouddocument/flow.svg';
import smartdocsIcon from '@/assets/channel/clouddocument/smartdocs.svg';
import folderIcon from '@/assets/channel/clouddocument/folder.png';
import apiTableIcon from '@/assets/channel/clouddocument/apiTable.svg';
import wordShortCut from '@/assets/channel/clouddocument/wordShortCut.svg';
import excelShortCut from '@/assets/channel/clouddocument/excelShortCut.svg';
import pptShortCut from '@/assets/channel/clouddocument/pptShortCut.svg';
import pdfShortCut from '@/assets/channel/clouddocument/pdfShortCut.svg';
import flowShortCut from '@/assets/channel/clouddocument/flowShortCut.svg';
import mindShortCut from '@/assets/channel/clouddocument/mindShortCut.svg';
import markdownShortCut from '@/assets/channel/clouddocument/markdownShortCut.svg';
import smartdocsShortCut from '@/assets/channel/clouddocument/smartdocsShortCut.svg';
import apiTableShortCut from '@/assets/channel/clouddocument/apiTableShortCut.svg';
import {
  ConversationItem,
  MessageItem,
  MessageType,
  PublicUserItem,
} from '@ht/openim-wasm-client-sdk';
import { t } from 'i18next';
import { parse } from 'querystring';
import { IMSDK } from '@/layouts/BasicLayout';
import { filter, isEmpty } from 'lodash';
import { secondsToTime } from './common';

export const getPageQuery = () => parse(window.location.href.split('?')[1]);

// 需要在聊天框中出现的消息类型列表，先加这么多，需要的话注意添加
// export const isNeedShowInConversation = (message: MessageItem) => {
//   const MessageListToShow = [
//     MessageType.TextMessage,
//     MessageType.PictureMessage,
//     MessageType.VoiceMessage,
//     MessageType.VideoMessage,
//     MessageType.FileMessage,
//     MessageType.AtTextMessage,
//     MessageType.MergeMessage,
//     MessageType.CardMessage,
//     MessageType.LocationMessage,
//     MessageType.CustomMessage,
//     MessageType.TypingMessage,
//     MessageType.QuoteMessage,
//     MessageType.FaceMessage,
//     MessageType.MemberKicked,
//     MessageType.MemberInvited,
//     MessageType.GroupDismissed,
//     MessageType.MemberQuit,
//     MessageType.GroupNameUpdated,
//     MessageType.RevokeMessage,
//   ];
//   return MessageListToShow.includes(message.contentType);
// };

// 查询默认的thread conversation的方法，目前用到这两个，用到更多时，需记得扩展
export const getDefaultThreadConversation = (threadID: string) => {
  return {
    groupID: threadID,
    conversationID: `sg_${threadID}`,
  };
};

export const getShowDiffTime = (time: number) => {
  try {
    if (time == null) {
      return '';
    }
    const currentTime = new Date().getTime();
    const targetTime = new Date(time).getTime();
    const diffTime = (currentTime - targetTime) / (1000 * 60); // 将毫秒转换为分钟

    if (diffTime < 0) {
      return '';
    } else if (diffTime < 1) {
      return '刚刚';
    } else if (diffTime < 60) {
      // 如果小于60分钟，则显示具体分钟数
      return `${Math.floor(diffTime)} 分钟前`;
    } else if (diffTime < 1440) {
      // 如果小于24小时（1440分钟），则显示小时和分钟
      const hours = Math.floor(diffTime / 60);
      return `${hours} 小时前`;
    } else {
      const days = Math.floor(diffTime / 60 / 24);
      return `${days} 天前`;
    }
  } catch (e) {
    console.error('转换时间失败，原因为：', e);
    return '';
  }
};

export const formatMessageByType = (
  message: MessageItem,
  selfUserID: string
): string => {
  if (!message) {
    return '';
  }
  const isSelf = (id: string) => id === selfUserID;
  const getName = (user: PublicUserItem) => {
    return user.userID === selfUserID ? t('you') : user.nickname;
  };
  try {
    switch (message.contentType) {
      case MessageType.TextMessage:
        return message.textElem!.content;
      case MessageType.AtTextMessage:
        let mstr = message.atTextElem!.text;
        const pattern = /@\S+\s/g;
        const arr = mstr.match(pattern);
        arr?.map((a) => {
          const member = (message.atTextElem!.atUsersInfo ?? []).find(
            (gm) => gm.atUserID === a.slice(1, -1)
          );
          if (member) {
            const reg = new RegExp(a, 'g');
            mstr = mstr.replace(reg, `@${member.groupNickname} `);
          }
        });
        return mstr;
      case MessageType.PictureMessage:
        return t('messageDescription.imageMessage');
      case MessageType.VideoMessage:
        return t('messageDescription.videoMessage');
      case MessageType.VoiceMessage:
        return t('messageDescription.voiceMessage');
      case MessageType.LocationMessage:
        const locationInfo = JSON.parse(message.locationElem!.description);
        return t('messageDescription.locationMessage', {
          location: locationInfo.name,
        });
      case MessageType.CardMessage:
        return t('messageDescription.cardMessage');
      case MessageType.MergeMessage:
        return t('messageDescription.mergeMessage');
      case MessageType.FileMessage:
        return t('messageDescription.fileMessage', {
          file: message.fileElem!.fileName,
        });
      case MessageType.RevokeMessage:
        const data = JSON.parse(message.notificationElem!.detail);
        const operator = isSelf(data.revokerID)
          ? t('you')
          : data.revokerNickname;
        const revoker = isSelf(data.sourceMessageSendID)
          ? t('you')
          : data.sourceMessageSenderNickname;

        const isAdminRevoke = data.revokerID !== data.sourceMessageSendID;
        if (isAdminRevoke) {
          return t('messageDescription.advanceRevokeMessage', {
            operator,
            revoker,
          });
        }
        return t('messageDescription.revokeMessage', { revoker });
      case MessageType.CustomMessage:
        return t('messageDescription.customMessage');
      case MessageType.QuoteMessage:
        return message.quoteElem!.text || t('messageDescription.quoteMessage');
      case MessageType.FaceMessage:
        return t('messageDescription.faceMessage');
      case MessageType.FriendAdded:
        return t('messageDescription.alreadyFriendMessage');
      case MessageType.MemberEnter:
        const enterDetails = JSON.parse(message.notificationElem!.detail);
        const enterUser = enterDetails.entrantUser;
        return t('messageDescription.joinGroupMessage', {
          name: getName(enterUser),
        });
      case MessageType.GroupCreated:
        const groupCreatedDetail = JSON.parse(message.notificationElem!.detail);
        const groupCreatedUser = groupCreatedDetail.opUser;
        return t('messageDescription.createGroupMessage', {
          creator: getName(groupCreatedUser),
        });
      case MessageType.MemberInvited:
        const inviteDetails = JSON.parse(message.notificationElem!.detail);
        const inviteOpUser = inviteDetails.opUser;
        const invitedUserList = inviteDetails.invitedUserList ?? [];
        let inviteStr = '';
        invitedUserList
          .slice(0, 3)
          .map((user: any) => (inviteStr += `${getName(user)}、`));
        inviteStr = inviteStr.slice(0, -1);
        return t('messageDescription.invitedToGroupMessage', {
          operator: getName(inviteOpUser),
          invitedUser: `${inviteStr}${invitedUserList.length > 3
              ? `${t('placeholder.and')}${t('placeholder.somePerson', {
                num: invitedUserList.length,
              })}`
              : ''
            }`,
        });
      case MessageType.MemberKicked:
        const kickDetails = JSON.parse(message.notificationElem!.detail);
        const kickOpUser = kickDetails.opUser;
        const kickdUserList = kickDetails.kickedUserList ?? [];
        let kickStr = '';
        kickdUserList
          .slice(0, 3)
          .map((user: any) => (kickStr += `${getName(user)}、`));
        kickStr = kickStr.slice(0, -1);
        return t('messageDescription.kickInGroupMessage', {
          operator: getName(kickOpUser),
          kickedUser: `${kickStr}${kickdUserList.length > 3
              ? `${t('placeholder.and')}${t('placeholder.somePerson', {
                num: kickdUserList.length,
              })}`
              : ''
            }`,
        });
      case MessageType.MemberQuit:
        const quitDetails = JSON.parse(message.notificationElem!.detail);
        const { quitUser } = quitDetails;
        return t('messageDescription.quitGroupMessage', {
          name: getName(quitUser),
        });
      case MessageType.GroupInfoUpdated:
        const groupUpdateDetail = JSON.parse(message.notificationElem!.detail);
        const groupUpdateUser = groupUpdateDetail.opUser;
        return t('messageDescription.updateGroupInfoMessage', {
          operator: getName(groupUpdateUser),
        });
      case MessageType.GroupOwnerTransferred:
        const transferDetails = JSON.parse(message.notificationElem!.detail);
        const transferOpUser = transferDetails.opUser;
        const newOwner = transferDetails.newGroupOwner;
        return t('messageDescription.transferGroupMessage', {
          owner: getName(transferOpUser),
          newOwner: getName(newOwner),
        });
      case MessageType.GroupDismissed:
        const dismissDetails = JSON.parse(message.notificationElem!.detail);
        const dismissUser = dismissDetails.opUser;
        return t('messageDescription.disbanedGroupMessage', {
          operator: getName(dismissUser),
        });
      case MessageType.GroupMuted:
        const GROUPMUTEDDetails = JSON.parse(message.notificationElem!.detail);
        const groupMuteOpUser = GROUPMUTEDDetails.opUser;
        return t('messageDescription.allMuteMessage', {
          operator: getName(groupMuteOpUser),
        });
      case MessageType.GroupCancelMuted:
        const GROUPCANCELMUTEDDetails = JSON.parse(
          message.notificationElem!.detail
        );
        const groupCancelMuteOpUser = GROUPCANCELMUTEDDetails.opUser;
        return t('messageDescription.cancelAllMuteMessage', {
          operator: getName(groupCancelMuteOpUser),
        });
      case MessageType.GroupMemberMuted:
        const gmMutedDetails = JSON.parse(message.notificationElem!.detail);
        const muteTime = secondsToTime(gmMutedDetails.mutedSeconds);
        return t('messageDescription.singleMuteMessage', {
          operator: getName(gmMutedDetails.opUser),
          name: getName(gmMutedDetails.mutedUser),
          muteTime,
        });
      case MessageType.GroupMemberCancelMuted:
        const gmcMutedDetails = JSON.parse(message.notificationElem!.detail);
        return t('messageDescription.cancelSingleMuteMessage', {
          operator: getName(gmcMutedDetails.opUser),
          name: getName(gmcMutedDetails.mutedUser),
        });
      case MessageType.GroupAnnouncementUpdated:
        const groupAnnouncementDetails = JSON.parse(
          message.notificationElem!.detail
        );
        return t('messageDescription.updateGroupAnnouncementMessage', {
          operator: getName(groupAnnouncementDetails.opUser),
        });
      case MessageType.GroupNameUpdated:
        const groupNameDetails = JSON.parse(message.notificationElem!.detail);
        return t('messageDescription.updateGroupNameMessage', {
          operator: getName(groupNameDetails.opUser),
          name: groupNameDetails.group.groupName,
        });
      case MessageType.OANotification:
        const customNoti = JSON.parse(message.notificationElem!.detail);
        return customNoti.text;
      case MessageType.BurnMessageChange:
        const burnDetails = JSON.parse(message.notificationElem!.detail);
        return t('messageDescription.burnReadStatus', {
          status: burnDetails.isPrivate ? t('open') : t('close'),
        });
      default:
        return '特殊消息类型';
    }
  } catch (error) {
    return '';
  }
};

export const setBrowserIcon = (iconUrl?: string) => {
  // 获取head标签
  const head = document.querySelector('head');
  if (!head) {
    return;
  }
  // 查找是否已有icon的link标签，并移除
  let link: HTMLLinkElement | null =
    document.querySelector('link[rel~="icon"]');
  if (link) {
    head.removeChild(link);
  }
  if (!iconUrl) {
    return;
  }

  // 创建新的link标签并设置icon的url
  link = document.createElement('link');
  link.rel = 'icon';
  link.href = iconUrl;

  // 将新的link标签添加到head标签中
  head.appendChild(link);
};

export const parseThreadIdFromMessage = (messageInfo: MessageItem) => {
  try {
    if (messageInfo == null || messageInfo?.thread == null) {
      return null;
    }

    const threadInfo = messageInfo.thread;

    return threadInfo?.threadId;
  } catch (e) {
    console.error('parseThreadIdFromMessage 失败', e);
    return null;
  }
};

export enum docTypeEnum {
  WORD = 1,
  EXCEL = 2,
  PPT = 3,
  PDF = 4,
  MINDMAP = 7, // 脑图
  MARKDOWN = 8,
  FLOWMAP = 9, // drawio
  SmartDocs = 10, // 块编辑器
  MINDMAPNEW = 11, // 脑图-新
  FOLDER = 99,
  TEMPLATE = 101,
  APITABLE = 12,
}

export const getDocIcon = (type: number | string, isShortCuts = false) => {
  switch (type) {
    case docTypeEnum.WORD:
      return isShortCuts ? wordShortCut : wordIcon;
    case docTypeEnum.EXCEL:
      return isShortCuts ? excelShortCut : excelIcon;
    case docTypeEnum.PPT:
      return isShortCuts ? pptShortCut : pptIcon;
    case docTypeEnum.PDF:
      return isShortCuts ? pdfShortCut : pdfIcon;
    case docTypeEnum.MINDMAP:
      return isShortCuts ? mindShortCut : mindIcon;
    case docTypeEnum.MINDMAPNEW:
      return isShortCuts ? mindShortCut : mindIcon;
    case docTypeEnum.FLOWMAP:
      return isShortCuts ? flowShortCut : flowIcon;
    case docTypeEnum.MARKDOWN:
      return isShortCuts ? markdownShortCut : markdownIcon;
    case docTypeEnum.SmartDocs:
      return isShortCuts ? smartdocsShortCut : smartdocsIcon;
    case docTypeEnum.FOLDER:
      return folderIcon;
    case docTypeEnum.APITABLE:
      return isShortCuts ? apiTableShortCut : apiTableIcon;
    default:
      return '';
  }
};

// 递归收起子节点
export const filterExpandkeys = (expandedKey: any, node: any) => {
  if (node.children && node.children.length > 0) {
    node.children.map((c: any) => {
      // eslint-disable-next-line no-param-reassign
      expandedKey = expandedKey.filter((e: any) => {
        let f = true;
        // eslint-disable-next-line eqeqeq
        if (c.id == e) {
          f = false;
        }
        if (f) {
          return e;
        }
      });
      if (node.children.children && node.children.children.length > 0) {
        filterExpandkeys(expandedKey, node.children);
      }
    });
  }
  return expandedKey;
};

// 递归树曾加子节点
export const updateTreeData = (
  list: any[],
  key: React.Key,
  children: any[]
): any => {
  list.forEach((node) => {
    if (node.key === key) {
      node.children = [...children];
    } else if (node.children) {
      node.children = [...updateTreeData(node.children, key, children)];
    }
  });
  return list;
};

// 搜索人员
export const searchUserByInputValue = async (
  keyword: string,
  pageSize = 10
) => {
  try {
    if (!keyword) {
      return;
    }
    const { data } = await IMSDK.searchEmployeeListPage(keyword, 0, pageSize);
    return data.list || [];
  } catch (e) {
    console.error('搜索人员失败', e);
    return [];
  }
};

export interface SearchedConversationType {
  groupID: string;
  showName: string;
  nickname: string;
  keyword: string;
}
// 搜索群会话

type SearchedConversationFuncType = (
  val: string
) => Promise<SearchedConversationType[]>;

export const searchGroupConversationByInputValue: SearchedConversationFuncType =
  async (keyword: string) => {
    const { data = [] } = await IMSDK.getAllConversationList();
    const filterData = filter(data, (item: ConversationItem) => {
      return (
        item.showName.toLowerCase().includes(keyword.toLowerCase()) &&
        !isEmpty(item.groupID)
      );
    });
    const groupData = filterData.map((i) => {
      return {
        groupID: i.groupID,
        showName: i.showName,
        nickname: '',
        keyword: '',
      };
    });
    const res: any = await IMSDK.getGroupsByNickname(keyword);

    const groupsByNickname: any[] = res.data || [];
    groupsByNickname.forEach((item: any) => {
      const index = groupData.findIndex(
        (i) => i.groupID === item?.group?.groupID
      );
      if (index >= 0) {
        groupData[index] = {
          ...groupData[index],
          nickname: item.nickname,
        };
      } else {
        groupData.push({
          groupID: item.group.groupID,
          showName: item.group.groupName,
          nickname: item.nickname,
          keyword: item.keyword,
        });
      }
    });
    return groupData;
  };

/**
 * 【临时用，后续务必移除】合并消息列表，按顺序将 newList 插入到 baseList 中，按 msgID 去重
 * @param baseList 已有消息列表
 * @param newList  新插入的消息列表
 * @returns 去重合并后的消息列表
 */
export const mergeMessagesByClientMsgID = (
  baseList: MessageItem[],
  newList: MessageItem[]
): MessageItem[] => {
  const existingIDs = new Set(baseList.map((msg) => msg.clientMsgID));
  const filtered = newList.filter((msg) => !existingIDs.has(msg.clientMsgID));
  return [...baseList, ...filtered];
};

/**
 * 获取该消息对应的conversationID
 * @param message
 * @returns
 */
export function getConversationIDByMsg(message: MessageItem) {
  const {
    sendID,
    recvID,
    subConversationId = '',
    sessionType,
    groupID,
  } = message || {};
  switch (sessionType) {
    case 1:
      return genConversationIDForSigle(
        sendID,
        recvID,
        subConversationId,
        'si_'
      );
    case 3:
      return `sg_${groupID}`;
    default:
      return '';
  }
}
function genConversationIDForSigle(
  sendID: string,
  recvID: string,
  subConversationID: string,
  prefix: string
) {
  const l = [sendID, recvID].sort();
  if (!subConversationID) {
    return prefix + l.join('_');
  }
  return `si_${l.join('_')}@${subConversationID}`;
}

// 是否是单会话页面
export const isSingleChat = () => {
  return (
    location.pathname === '/linkflow/singleChat' ||
    location.pathname === '/linkflow/smartAssistantHalfPage' ||
    location.pathname === '/linkflow/smartAssistantFullPage' ||
    location.pathname === '/linkflow/emptyPage' ||
    location.pathname === '/htscPortal/home'
  );
};

// 是否在智能助理页面
export const isInSmartAssitantantPage = () => {
  return (
    location.pathname === '/linkflow/smartAssistantHalfPage' ||
    location.pathname === '/linkflow/smartAssistantFullPage' ||
    location.pathname === '/htscPortal/home'
  );
};

// 是否在智能助理半屏页面
export const isInSmartAssitantantHalfPage = () => {
  return (
    location.pathname === '/linkflow/smartAssistantHalfPage' ||
    location.pathname === '/htscPortal/home'
  );
};

// 是否在智能助理全屏页面
export const isInSmartAssitantantFullPage = () => {
  return location.pathname === '/linkflow/smartAssistantFullPage';
};

export const ifNavigarCanGetUserMedia = () => {
  if (navigator?.mediaDevices?.getUserMedia != null) {
    return true;
  } else {
    return false;
  }
};
