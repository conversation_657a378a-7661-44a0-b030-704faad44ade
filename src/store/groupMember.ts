import { GroupMemberItem } from '@ht/openim-wasm-client-sdk/lib/types/entity';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { IMSDK } from '@/layouts/BasicLayout';

interface GroupMemberStore {
  groupMemberList?: GroupMemberItem[];
  loading: boolean;

  setGroupMemberList: (list: GroupMemberItem[]) => void;

  updateGroupMemberInfo: (data: GroupMemberItem) => void;
  initCurrentGroupMemberList: (
    groupID: string,
    forceLoading: boolean
  ) => Promise<void>;

  resetState: () => void;
}

// currentConversation对应Group的群成员信息
export const useGroupMembersStore = create<GroupMemberStore>()(
  devtools(
    (set, get) => ({
      groupMemberList: [],
      loading: false,

      updateGroupMemberInfo: (member) => {
        if (member.groupID) {
          const idx =
            get()?.groupMemberList?.findIndex(
              (item) => item.userID === member.userID
            ) || -1;
          const newMembers = [...(get()?.groupMemberList || [])];
          newMembers[idx] = { ...member };
          set((state) => ({
            ...state,
            groupMemberList: newMembers,
          }));
        }
      },

      initCurrentGroupMemberList: async (groupID, forceLoading = false) => {
        if (!groupID) {
          return;
        }
        if (get().loading && !forceLoading) {
          return;
        }
        set({ loading: true });
        try {
          let offset = 0;
          let tmpList: GroupMemberItem[] = [];
          const BATCH_COUNT = 500;

          while (true) {
            const count = BATCH_COUNT;

            const { data } = await IMSDK.getGroupMemberList({
              groupID,
              offset,
              count,
              filter: 0,
            });

            tmpList = [...tmpList, ...data];
            offset += data.length;

            if (data.length < count) {
              break;
            }
          }

          set({ groupMemberList: tmpList });
        } catch (error) {
          console.error('获取群成员失败', error);
        } finally {
          set({ loading: false });
        }
      },

      setGroupMemberList: (list) => set({ groupMemberList: list }),

      resetState: () => {
        set({ groupMemberList: [], loading: false });
      },
    }),
    {
      name: 'groupMemberStore',
      enabled: true,
    }
  )
);
