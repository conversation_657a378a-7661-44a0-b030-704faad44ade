import { create } from 'zustand';
import _ from 'lodash';
import { devtools } from 'zustand/middleware';
import { SmartAssitantStore, CHANGE_SMART_RIGHT_ARE } from './type';

export const useSmartAssitantStore = create<SmartAssitantStore>()(
  devtools(
    (set, get) => ({
      rightAreaInSmart: null,
      rightAreaData: null,
      fromMsgID: undefined,
      changeRightArea: async (
        changeRightAction: CHANGE_SMART_RIGHT_ARE | '',
        payload?: any
      ) => {
        if (changeRightAction !== '') {
          set(() => {
            return {
              rightAreaInSmart: changeRightAction,
              rightAreaData: { data: payload?.data },
              fromMsgID: payload?.fromMsgID,
            };
          });
        } else {
          get().clearRightArea();
        }
      },
      clearRightArea: () => {
        set(() => {
          return {
            rightAreaInSmart: null,
            rightAreaData: null,
            fromMsgID: undefined,
          };
        });
      },
      selectObj: undefined,
      setSelectObj: (obj) => {
        set(() => {
          return {
            selectObj: obj,
          };
        });
      },
      curMessageListLength: 0, // 当前会话消息记录条数
      changeCurMessageListLength: (val: number) => {
        set(() => {
          return {
            curMessageListLength: val,
          };
        });
      },
    }),
    {
      name: 'smartAssitantStore',
      enabled: true,
    }
  )
);
