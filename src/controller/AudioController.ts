import { ttlUrl } from '@config/imconfig';
import { EventEmitter } from 'events';
import PCMPlayer from 'pcm-player';

export type AudioState = 'idle' | 'converting' | 'playing';

class AudioController extends EventEmitter {
  private player: PCMPlayer | null = null;

  private tts: any = null;

  private _state: AudioState = 'idle';

  private _currentId: string = '';

  private _currentPlayKey: string = '';

  // 核心状态：仅保留“最后一段标记”和“兜底延迟计时器”
  private isLastDataReceived: boolean = false;

  private finalEndTimer: NodeJS.Timeout | null = null;

  get state() {
    return this._state;
  }

  get currentId() {
    return this._currentId;
  }

  private setState(state: AudioState, id?: string) {
    this._state = state;
    if (id !== undefined) {
      this._currentId = id;
    }
    this.emit('stateChange', this._state, this._currentId);
  }

  // 重置状态：重点清除计时器和标记
  private resetStates() {
    this._currentPlayKey = '';
    this.isLastDataReceived = false;
    if (this.finalEndTimer) {
      clearTimeout(this.finalEndTimer);
      this.finalEndTimer = null;
    }
  }

  stop() {
    // 终止播放实例
    this.tts?.stop?.();
    this.player?.destroy?.();
    this.tts = null;
    this.player = null;

    // 重置所有状态
    const wasIdle = this._state === 'idle';
    this.resetStates();
    if (!wasIdle) {
      this.setState('idle', '');
    }
  }

  play(id: string, text: string) {
    this.stop(); // 停旧播放

    const playKey = `${id}_${Date.now()}`;
    this._currentPlayKey = playKey;
    this.setState('converting', id);

    // 1. 创建播放器：仅保留基础配置，忽略 onEnded 复杂逻辑
    const player = new PCMPlayer({
      inputCodec: 'Int16',
      channels: 1,
      sampleRate: 16000,
      flushTime: 1000,
      fftSize: 2048,
      onended: () => {
        // onEnded 仅作为“辅助信号”：如果最后一段已接收，触发延迟检查
        if (this._currentPlayKey === playKey && this.isLastDataReceived) {
          this.triggerFinalEndCheck(playKey);
        }
      },
    });
    this.player = player;

    let hasStarted = false;

    // 2. TTS 数据回调：仅处理“数据喂入”和“最后一段标记”
    const pcmPlay = (isEnd: boolean, audio: any) => {
      if (this._currentPlayKey !== playKey) {
        return;
      }

      if (audio?.length > 0) {
        // 首次播放切换状态
        if (!hasStarted) {
          hasStarted = true;
          this.setState('playing', id);
        }
        // 喂数据（简化格式处理）
        const segment = new Int8Array(audio.length);
        for (let i = 0; i < audio.length; i++) {
          segment[i] = audio.charCodeAt(i);
        }
        player.feed(segment.buffer);

        // 有新数据时，清除之前的兜底延迟（避免新数据被误判）
        if (this.finalEndTimer) {
          clearTimeout(this.finalEndTimer);
          this.finalEndTimer = null;
        }
      }

      // 3. 收到最后一段：标记状态 + 启动兜底延迟
      if (isEnd) {
        this.isLastDataReceived = true;
        this.triggerFinalEndCheck(playKey);
      }
    };

    // 4. TTS 初始化和错误处理
    // @ts-expect-error
    const tts = new Tts(pcmPlay);
    this.tts = tts;

    tts.onError(() => {
      if (this._currentPlayKey !== playKey) {
        return;
      }
      this.stop();
    });

    // 5. 启动 TTS
    tts.start(
      ttlUrl,
      {
        svc: 'tts',
        auf: '4',
        vid: '500861',
        aue: 'raw',
        type: '1',
        uid: '660Y5r',
        appid: 'pc20onli',
        extend_params: '{"params":"spd=0,token=anhui,ability=ab_tts"}',
      },
      text,
      false
    );
  }

  /**
   * 核心兜底逻辑：最后一段接收后，等 1.5 秒再判定结束
   * 1.5 秒 = 播放器 flushTime(1s) + 预留 0.5s 缓冲残留（可根据实际调整）
   */
  private triggerFinalEndCheck(playKey: string) {
    if (this._currentPlayKey !== playKey) {
      return;
    }

    // 清除旧计时器，避免重复触发
    if (this.finalEndTimer) {
      clearTimeout(this.finalEndTimer);
    }

    // 启动兜底延迟：1.5 秒后确认结束
    this.finalEndTimer = setTimeout(() => {
      this.setState('idle', '');
      this.resetStates();
      this.emit('playEnd');
    }, 1500); // 关键参数：可根据 TTS 语速调整（如 2000ms 更保险）
  }
}

export const audioController = new AudioController();
